import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Form, FormItem, Input, Textarea, IconCard } from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/useFormErrors';
import { useCreatePhysicalWarehouseWithDetails } from '../../hooks/usePhysicalWarehouseQuery';
import {
  CreatePhysicalWarehouseWithDetailsDto,
  CustomFieldInputDto,
} from '../../types/warehouse.types';
import {
  createPhysicalWarehouseWithDetailsSchemaComplete,
  CreatePhysicalWarehouseWithDetailsFormValues,
} from '../../schemas/physical-warehouse.schema';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import WarehouseCustomFields from './sections/WarehouseCustomFields';

interface CreateWarehouseWithDetailsFormProps {
  initialData?: Partial<CreatePhysicalWarehouseWithDetailsFormValues>;
  onSubmit?: () => void;
  onCancel?: () => void;
}

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  configId: string;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
  isLoadingConfig?: boolean;
}

/**
 * Component form tạo kho mới với API endpoint mới
 */
const CreateWarehouseWithDetailsForm: React.FC<CreateWarehouseWithDetailsFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const notification = useSmartNotification();

  // Sử dụng useFormErrors để quản lý form và lỗi
  const { formRef, setFormErrors } = useFormErrors<CreatePhysicalWarehouseWithDetailsFormValues>();

  // State cho custom fields
  const [warehouseCustomFields, setWarehouseCustomFields] = useState<SelectedCustomField[]>([]);

  // Mutation để tạo kho mới
  const { mutateAsync: createWarehouse, isPending: isCreating } =
    useCreatePhysicalWarehouseWithDetails();

  // Default values cho form
  const defaultValues: Partial<CreatePhysicalWarehouseWithDetailsFormValues> = {
    name: '',
    description: '',
    address: '',
    capacity: undefined,
    ...initialData,
  };

  // Xử lý submit form
  const handleSubmit = async (data: CreatePhysicalWarehouseWithDetailsFormValues) => {
    try {
      // Chuẩn bị custom fields data
      const customFields: CustomFieldInputDto[] = warehouseCustomFields.map(field => ({
        customFieldId: field.fieldId,
        value: {
          value: field.value['value'] as string | number | boolean,
        },
      }));

      // Chuẩn bị data để gửi API
      const submitData: CreatePhysicalWarehouseWithDetailsDto = {
        name: data.name,
        address: data.address,
        ...(data.description && { description: data.description }),
        ...(data.capacity && { capacity: Number(data.capacity) }),
        ...(customFields.length > 0 && { customFields }),
      };

      await createWarehouse(submitData);

      notification.success({
        message: t('business:warehouse.createSuccess', 'Tạo kho thành công'),
        duration: 3000,
      });

      // Reset form và custom fields
      formRef.current?.reset();
      setWarehouseCustomFields([]);

      // Gọi callback onSubmit nếu có
      if (onSubmit) {
        onSubmit();
      }
    } catch (error: any) {
      console.error('Error creating warehouse:', error);

      // Xử lý lỗi từ backend
      const errorData = error?.response?.data || error?.data || error;

      // Xử lý lỗi code 30042 - Tên kho đã tồn tại
      if (errorData?.code === 30042) {
        setFormErrors({
          name: errorData.message || 'Tên kho đã tồn tại',
        });
        return;
      }

      // Xử lý các lỗi validation khác có thể map được vào field cụ thể
      if (errorData?.code) {
        switch (errorData.code) {
          case 30041: // Giả sử có lỗi địa chỉ không hợp lệ
            setFormErrors({
              address: errorData.message || 'Địa chỉ kho không hợp lệ',
            });
            return;
          case 30043: // Giả sử có lỗi sức chứa không hợp lệ
            setFormErrors({
              capacity: errorData.message || 'Sức chứa kho không hợp lệ',
            });
            return;
        }
      }

      // Xử lý các lỗi khác từ backend
      if (errorData?.message) {
        notification.error({
          message: errorData.message,
          duration: 3000,
        });
        return;
      }

      // Lỗi mặc định
      notification.error({
        message: t('business:warehouse.createError', 'Lỗi khi tạo kho'),
        duration: 3000,
      });
    }
  };

  // Xử lý hủy
  const handleCancel = () => {
    formRef.current?.reset();
    setWarehouseCustomFields([]);
    if (onCancel) {
      onCancel();
    }
  };

  return (
    <Card title={t('business:warehouse.add', 'Thêm kho')}>
      <Form
        ref={formRef as any}
        schema={createPhysicalWarehouseWithDetailsSchemaComplete}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleSubmit as any}
        defaultValues={defaultValues}
        className="p-4 space-y-6"
      >
        <div className="grid grid-cols-1 gap-4">
          {/* Tên kho */}
          <FormItem name="name" label={t('business:warehouse.name', 'Tên kho')} required>
            <Input
              fullWidth
              placeholder={t('business:warehouse.form.namePlaceholder', 'Nhập tên kho')}
            />
          </FormItem>

          {/* Mô tả kho */}
          <FormItem name="description" label={t('business:warehouse.description', 'Mô tả kho')}>
            <Textarea
              fullWidth
              placeholder={t('business:warehouse.form.descriptionPlaceholder', 'Nhập mô tả kho')}
              rows={3}
            />
          </FormItem>

          {/* Địa chỉ kho */}
          <FormItem name="address" label={t('business:warehouse.address', 'Địa chỉ kho')} required>
            <Input
              fullWidth
              placeholder={t('business:warehouse.form.addressPlaceholder', 'Nhập địa chỉ kho')}
            />
          </FormItem>

          {/* Sức chứa kho */}
          <FormItem name="capacity" label={t('business:warehouse.capacity', 'Sức chứa kho')}>
            <Input
              fullWidth
              type="number"
              min="0"
              placeholder={t('business:warehouse.form.capacityPlaceholder', 'Nhập sức chứa kho')}
            />
          </FormItem>
        </div>

        {/* Custom Fields */}
        <WarehouseCustomFields
          warehouseCustomFields={warehouseCustomFields}
          onCustomFieldsChange={setWarehouseCustomFields}
        />

        {/* Buttons */}
        <div className="flex justify-end space-x-4 pt-4">
          <IconCard
            icon="x"
            variant="ghost"
            onClick={handleCancel}
            disabled={isCreating}
          />
          <IconCard
            icon="check"
            variant="primary"
            onClick={() => formRef.current?.submit()}
            isLoading={isCreating}
            disabled={isCreating}
          />
        </div>
      </Form>
    </Card>
  );
};

export default CreateWarehouseWithDetailsForm;
