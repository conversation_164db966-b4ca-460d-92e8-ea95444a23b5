import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  CollapsibleCard,
  Typography,
  FormItem,
  Input,
  Select,
} from '@/shared/components/common';

const PricingSection: React.FC = () => {
  const { t } = useTranslation(['business', 'common']);
  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="font-medium">
          {t('business:product.form.sections.pricing', '2. Giá sản phẩm')}
        </Typography>
      }
      defaultOpen={true}
      className="mb-4"
    >
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <FormItem name="currency" label={t('business:product.form.currency')} required>
            <Select
              fullWidth
              options={[
                { value: 'VND', label: 'VND' },
                { value: 'USD', label: 'USD' },
                { value: 'EUR', label: 'EUR' },
              ]}
            />
          </FormItem>

          <FormItem name="listPrice" label={t('business:product.form.listPrice')} required>
            <Input fullWidth type="number" min="0" placeholder="0" />
          </FormItem>

          <FormItem name="salePrice" label={t('business:product.form.salePrice')} required>
            <Input fullWidth type="number" min="0" placeholder="0" />
          </FormItem>
        </div>
      </div>
    </CollapsibleCard>
  );
};

export default PricingSection;
