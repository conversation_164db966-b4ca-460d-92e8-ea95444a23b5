import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { SortDirection } from '@/shared/dto/request/query.dto';
import {
  ShopInfoDto,
  CreateOrUpdateShopInfoDto,
} from '../types/shop.types';
import { filterTruthyParams } from '@/shared/utils/param-utils';

/**
 * Service xử lý API liên quan đến thông tin cửa hàng
 */
export const ShopService = {
  /**
   * Lấy danh sách cửa hàng
   * @param params Query parameters cho tìm kiếm và phân trang
   * @returns Danh sách cửa hàng
   */
  getShopList: async (params?: {
    page?: number | undefined;
    limit?: number | undefined;
    search?: string | undefined;
    sortBy?: string | undefined;
    sortDirection?: SortDirection | undefined;
  }): Promise<ApiResponseDto<{items: ShopInfoDto[]}>> => {
    const filteredParams = filterTruthyParams(params);
    return apiClient.get('/user/shop-info/all', { params: filteredParams });
  },

  /**
   * Lấy thông tin cửa hàng đơn lẻ (giữ lại để tương thích)
   * @returns Thông tin cửa hàng
   */
  getShopInfo: async (): Promise<ApiResponseDto<ShopInfoDto>> => {
    return apiClient.get('/user/shop-info');
  },

  /**
   * Lấy thông tin chi tiết cửa hàng theo ID
   * @param id ID của cửa hàng
   * @returns Thông tin chi tiết cửa hàng
   */
  getShopDetail: async (id: string): Promise<ApiResponseDto<ShopInfoDto>> => {
    return apiClient.get(`/user/shop-info/${id}`);
  },

  /**
   * Lấy thông tin shop theo ID
   * @param id ID của shop
   * @returns Thông tin shop
   */
  getShopById: async (id: string): Promise<ApiResponseDto<ShopInfoDto>> => {
    return apiClient.get(`/user/shop-info/shop/${id}`);
  },

  /**
   * Tạo thông tin cửa hàng mới
   * @param data Dữ liệu cửa hàng
   * @returns Thông tin cửa hàng được tạo
   */
  createShopInfo: async (
    data: CreateOrUpdateShopInfoDto
  ): Promise<ApiResponseDto<ShopInfoDto>> => {
    return apiClient.post('/user/shop-info/create', data);
  },

  /**
   * Cập nhật thông tin cửa hàng theo ID
   * @param id ID của cửa hàng
   * @param data Dữ liệu cập nhật
   * @returns Thông tin cửa hàng được cập nhật
   */
  updateShopInfo: async (
    id: string,
    data: CreateOrUpdateShopInfoDto
  ): Promise<ApiResponseDto<ShopInfoDto>> => {
    return apiClient.put(`/user/shop-info/${id}`, data);
  },

  /**
   * Xóa nhiều cửa hàng
   * @param shopIds Danh sách ID cửa hàng cần xóa
   * @returns Promise với kết quả xóa
   */
  deleteMultipleShops: async (shopIds: number[]): Promise<void> => {
    await apiClient.delete('/user/shop-info/multiple', {
      data: { shopIds }
    });
  },
};

export default ShopService;
