/**
 * Hook quản lý hiển thị cột trong bảng
 * @module useColumnVisibility
 */
import { useState, useEffect, useMemo, useCallback } from 'react';
import { TableColumn } from '@/shared/components/common/Table/types';

/**
 * Interface định nghĩa cấu trúc hiển thị cột
 */
export interface ColumnVisibility {
  id: string;
  label: string;
  visible: boolean;
}

/**
 * Interface cho tham số đầu vào của hook useColumnVisibility
 */
export interface UseColumnVisibilityOptions<T> {
  /**
   * Danh sách cột của bảng
   */
  columns: TableColumn<T>[];

  /**
   * C<PERSON> bao gồm tùy chọn "Tất cả" hay không
   * @default true
   */
  includeAllOption?: boolean;

  /**
   * Nhãn cho tùy chọn "Tất cả"
   * @default 'Tất cả'
   */
  allOptionLabel?: string;
}

/**
 * Hook quản lý hiển thị cột trong bảng
 * @template T Kiểu dữ liệu của dòng trong bảng
 * @param options Tùy chọn cho hook
 * @returns Các state và hàm xử lý cho hiển thị cột
 */
export function useColumnVisibility<T>(options: UseColumnVisibilityOptions<T>) {
  const { columns, includeAllOption = true, allOptionLabel = 'Tất cả' } = options;

  // Tạo danh sách hiển thị cột từ columns
  const initialColumnVisibility = useMemo<ColumnVisibility[]>(() => {
    const columnVisibility: ColumnVisibility[] = [];

    // Thêm option "Tất cả" nếu cần
    if (includeAllOption) {
      columnVisibility.push({ id: 'all', label: allOptionLabel, visible: true });
    }

    // Thêm các cột từ columns
    columns.forEach(column => {
      columnVisibility.push({
        id: column.key,
        label: typeof column.title === 'string' ? column.title : column.key,
        visible: true,
      });
    });

    return columnVisibility;
  }, [columns, includeAllOption, allOptionLabel]);

  // State cho danh sách hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>(initialColumnVisibility);

  // Cập nhật visibleColumns khi columns thay đổi
  useEffect(() => {
    // Tạo key để so sánh columns
    const columnsKey = columns.map(col => `${col.key}-${typeof col.title === 'string' ? col.title : col.key}`).join('|');

    // Sử dụng functional update để tránh dependency vào visibleColumns
    setVisibleColumns(prevColumns => {
      // Tạo key cho prevColumns để so sánh
      const prevColumnsKey = prevColumns
        .filter(col => col.id !== 'all')
        .map(col => `${col.id}-${col.label}`)
        .join('|');

      // Chỉ cập nhật nếu có thay đổi thực sự về cấu trúc
      if (columnsKey === prevColumnsKey && prevColumns.length > 0) {
        return prevColumns;
      }

      // Lưu trữ trạng thái visible hiện tại
      const currentVisibility: Record<string, boolean> = {};
      prevColumns.forEach(col => {
        currentVisibility[col.id] = col.visible;
      });

      // Tạo mảng visibleColumns mới
      const newVisibleColumns: ColumnVisibility[] = [];

      // Thêm option "Tất cả" nếu cần, giữ nguyên trạng thái
      if (includeAllOption) {
        newVisibleColumns.push({
          id: 'all',
          label: allOptionLabel,
          visible: currentVisibility['all'] !== undefined ? currentVisibility['all'] : true,
        });
      }

      // Thêm các cột từ columns, giữ nguyên trạng thái nếu đã tồn tại
      columns.forEach(column => {
        newVisibleColumns.push({
          id: column.key,
          label: typeof column.title === 'string' ? column.title : column.key,
          visible: Boolean(
            currentVisibility[column.key] !== undefined ? currentVisibility[column.key] : true
          ),
        });
      });

      return newVisibleColumns;
    });
  }, [columns, includeAllOption, allOptionLabel]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback(
    (columnId: string, checked: boolean) => {
      setVisibleColumns(prevColumns => {
        const updatedColumns = [...prevColumns];

        // Xử lý trường hợp "Chọn tất cả"
        if (columnId === 'all') {
          return updatedColumns.map(col => ({
            ...col,
            visible: checked,
          }));
        }

        // Tìm và cập nhật cột được chọn/bỏ chọn
        const columnToUpdate = updatedColumns.find(col => col.id === columnId);
        if (columnToUpdate) {
          columnToUpdate.visible = checked;
        }

        // Cập nhật trạng thái "Tất cả" nếu cần
        if (includeAllOption) {
          const allColumn = updatedColumns.find(col => col.id === 'all');
          if (allColumn) {
            // Chỉ chọn "Tất cả" khi tất cả các cột khác đều được chọn
            const otherColumns = updatedColumns.filter(col => col.id !== 'all');
            allColumn.visible = otherColumns.every(col => col.visible);
          }
        }

        return updatedColumns;
      });
    },
    [includeAllOption]
  );

  // Lọc các cột hiển thị dựa trên visibleColumns
  const visibleTableColumns = useMemo(() => {
    return columns.filter(col => {
      const columnSetting = visibleColumns.find(vc => vc.id === col.key);
      return columnSetting?.visible !== false;
    });
  }, [columns, visibleColumns]);

  return {
    // States
    visibleColumns,
    visibleTableColumns,

    // Handlers
    handleColumnVisibilityChange,
    setVisibleColumns,
  };
}
