import React from 'react';
import {
  CollapsibleCard,
  Typography,
  FormItem,
  Input,
} from '@/shared/components/common';
import { ShippingSectionProps } from './product-form-types';

const ShippingSection: React.FC<ShippingSectionProps> = ({ t }) => {
  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="font-medium">
          {t('business:product.form.sections.shipping', '4. Vận chuyển')}
        </Typography>
      }
      defaultOpen={false}
      className="mb-4"
    >
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem
            name="shipmentConfig.widthCm"
            label={t('business:product.form.shipmentConfig.widthCm', 'Chiều rộng (cm)')}
          >
            <Input fullWidth type="number" min="0" placeholder="0" />
          </FormItem>
          <FormItem
            name="shipmentConfig.heightCm"
            label={t('business:product.form.shipmentConfig.heightCm', 'Chiều cao (cm)')}
          >
            <Input fullWidth type="number" min="0" placeholder="0" />
          </FormItem>
          <FormItem
            name="shipmentConfig.lengthCm"
            label={t('business:product.form.shipmentConfig.lengthCm', 'Chiều dài (cm)')}
          >
            <Input fullWidth type="number" min="0" placeholder="0" />
          </FormItem>
          <FormItem
            name="shipmentConfig.weightGram"
            label={t('business:product.form.shipmentConfig.weightGram', 'Khối lượng (gram)')}
          >
            <Input fullWidth type="number" min="0" placeholder="0" />
          </FormItem>
        </div>
      </div>
    </CollapsibleCard>
  );
};

export default ShippingSection;
