import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  CollapsibleCard,
  Typography,
} from '@/shared/components/common';
import SimpleCustomFieldSelector from '@/modules/business/components/SimpleCustomFieldSelector';
import CustomFieldRenderer from '@/modules/business/components/CustomFieldRenderer';
import { CustomFieldsSectionProps } from './product-form-types';

const CustomFieldsSection: React.FC<CustomFieldsSectionProps> = ({
  productCustomFields,
  handleToggleCustomFieldToProduct,
  handleUpdateCustomFieldInProduct,
  handleRemoveCustomFieldFromProduct,
}) => {
  const { t } = useTranslation(['business', 'common']);
  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="font-medium">
          {t('business:product.form.sections.customFields', '7. Trường tùy chỉnh')}
        </Typography>
      }
      defaultOpen={false}
      className="mb-4"
    >
      <div className="space-y-4">
        <SimpleCustomFieldSelector
          onFieldSelect={fieldData => {
            handleToggleCustomFieldToProduct(
              fieldData.id,
              fieldData as unknown as Record<string, unknown>
            );
          }}
          selectedFieldIds={productCustomFields.map(f => f.fieldId)}
          placeholder={t(
            'business:product.form.customFields.searchPlaceholder',
            'Nhập từ khóa và nhấn Enter để tìm trường tùy chỉnh...'
          )}
        />

        {productCustomFields.length > 0 && (
          <div className="space-y-3">
            {productCustomFields.map(field => (
              <CustomFieldRenderer
                key={field.id}
                field={field}
                value={(field.value?.['value'] as string) || ''}
                onChange={value => handleUpdateCustomFieldInProduct(field.id, value as string)}
                onRemove={() => handleRemoveCustomFieldFromProduct(field.id)}
              />
            ))}
          </div>
        )}
      </div>
    </CollapsibleCard>
  );
};

export default CustomFieldsSection;
