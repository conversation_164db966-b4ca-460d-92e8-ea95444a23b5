import React, { useState, useEffect, forwardRef, useImperativeHandle, useRef } from 'react';
import { CountrySelect } from '@/shared/components/common';
import { Country, findCountryByCode } from '@/shared/data/countries';

export interface PhoneInputWithCountryProps {
  /**
   * Giá trị số điện thoại (có thể là số điện thoại thường hoặc số quốc tế)
   */
  value?: string;

  /**
   * Callback khi giá trị thay đổi - trả về số điện thoại quốc tế đầy đủ (ví dụ: +84987654321)
   */
  onChange?: (internationalPhone: string) => void;

  /**
   * Placeholder cho input số điện thoại
   */
  placeholder?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * Name attribute
   */
  name?: string;

  /**
   * ID attribute
   */
  id?: string;

  /**
   * CSS class
   */
  className?: string;

  /**
   * Error message
   */
  error?: string;

  /**
   * Helper text
   */
  helperText?: string;

  /**
   * Size
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Full width
   */
  fullWidth?: boolean;

  /**
   * Quốc gia mặc định (country code)
   */
  defaultCountry?: string;

  /**
   * Auto complete
   */
  autoComplete?: string;
}

/**
 * Component PhoneInputWithCountry - Input số điện thoại với chọn quốc gia
 */
const PhoneInputWithCountry = forwardRef<HTMLInputElement, PhoneInputWithCountryProps>(
  (
    {
      value = '',
      onChange,
      placeholder = '',
      disabled = false,
      name,
      id,
      className = '',
      error,
      helperText,
      size = 'md',
      fullWidth = false,
      defaultCountry = 'VN',
      autoComplete = 'off',
    },
    ref
  ) => {
    // State cho country selection
    const [selectedCountry, setSelectedCountry] = useState<string>(() => {
      // Lấy country từ localStorage hoặc default
      return localStorage.getItem('country') || defaultCountry;
    });

    const inputRef = useRef<HTMLInputElement>(null);

    // Forward ref to input element
    useImperativeHandle(ref, () => inputRef.current as HTMLInputElement);

    // Parse số điện thoại để tách country code và phone number
    const parsePhoneNumber = (phoneValue: string) => {
      if (!phoneValue) {
        return { countryCode: selectedCountry, phoneNumber: '' };
      }

      // Nếu đã có dấu +, parse để tách country code
      if (phoneValue.startsWith('+')) {
        // Tìm country code phù hợp
        if (phoneValue.startsWith('+84')) {
          return { countryCode: 'VN', phoneNumber: phoneValue.substring(3) };
        }
        if (phoneValue.startsWith('+1')) {
          return { countryCode: 'US', phoneNumber: phoneValue.substring(2) };
        }
        if (phoneValue.startsWith('+86')) {
          return { countryCode: 'CN', phoneNumber: phoneValue.substring(3) };
        }
        if (phoneValue.startsWith('+81')) {
          return { countryCode: 'JP', phoneNumber: phoneValue.substring(3) };
        }
        if (phoneValue.startsWith('+82')) {
          return { countryCode: 'KR', phoneNumber: phoneValue.substring(3) };
        }
        if (phoneValue.startsWith('+44')) {
          return { countryCode: 'GB', phoneNumber: phoneValue.substring(3) };
        }
        if (phoneValue.startsWith('+33')) {
          return { countryCode: 'FR', phoneNumber: phoneValue.substring(3) };
        }
        if (phoneValue.startsWith('+49')) {
          return { countryCode: 'DE', phoneNumber: phoneValue.substring(3) };
        }

        // Nếu không tìm thấy, có thể implement logic parse phức tạp hơn sau
        // Tạm thời giữ nguyên logic cũ

        // Nếu không tìm thấy, giữ nguyên
        return { countryCode: selectedCountry, phoneNumber: phoneValue.replace(/^\+\d+/, '') };
      }

      // Nếu không có dấu +, coi như là số điện thoại local (chỉ số)
      return { countryCode: selectedCountry, phoneNumber: phoneValue.replace(/[^\d]/g, '') };
    };

    // State cho phone number (không bao gồm country code)
    const [phoneNumber, setPhoneNumber] = useState<string>(() => {
      const { phoneNumber: parsed } = parsePhoneNumber(value);
      return parsed;
    });

    // Cập nhật phone number khi value từ bên ngoài thay đổi
    useEffect(() => {
      const { countryCode, phoneNumber: parsed } = parsePhoneNumber(value);
      setPhoneNumber(parsed);
      if (countryCode !== selectedCountry) {
        setSelectedCountry(countryCode);
      }
    }, [value]);

    // Handler cho country change
    const handleCountryChange = (country: Country) => {
      setSelectedCountry(country.code);
      // Lưu country vào localStorage
      localStorage.setItem('country', country.code);

      // Tạo số điện thoại quốc tế mới với country code mới
      if (onChange) {
        if (phoneNumber) {
          const selectedCountryData = findCountryByCode(country.code);
          const dialCode = selectedCountryData?.dialCode || '+84';
          const internationalPhone = `${dialCode}${phoneNumber}`;
          onChange(internationalPhone);
        } else {
          // Nếu chưa có phone number, chỉ gọi onChange với empty string
          onChange('');
        }
      }
    };

    // Handler cho phone input change
    const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newPhoneNumber = e.target.value;
      // Chỉ cho phép nhập số
      const cleanPhoneNumber = newPhoneNumber.replace(/[^\d]/g, '');
      setPhoneNumber(cleanPhoneNumber);

      if (onChange) {
        if (cleanPhoneNumber) {
          const selectedCountryData = findCountryByCode(selectedCountry);
          const dialCode = selectedCountryData?.dialCode || '+84';
          const internationalPhone = `${dialCode}${cleanPhoneNumber}`;
          onChange(internationalPhone);
        } else {
          onChange('');
        }
      }
    };

    // Lấy thông tin country hiện tại để hiển thị dial code
    const currentCountry = findCountryByCode(selectedCountry);
    const dialCode = currentCountry?.dialCode || '+84';

    return (
      <div className={`${fullWidth ? 'w-full' : ''} ${className}`}>
        <div className="flex border border-border rounded-md bg-card-muted focus-within:border-destructive">
          {/* Country Select - không có border, background trong suốt */}
          <div className="flex-shrink-0">
            <CountrySelect
              value={selectedCountry}
              onChange={handleCountryChange}
              compact={true}
              size={size}
              disabled={disabled}
              className="[&>div]:border-0 [&>div]:bg-transparent [&>div]:rounded-r-none [&>div]:focus:ring-0 [&>div]:focus:border-0 [&>div]:focus-within:ring-0 [&>div]:focus-within:border-0"
            />
          </div>

          {/* Separator */}
          <div className="w-px bg-border"></div>

          {/* Phone Input với dial code prefix */}
          <div className="flex-1 flex items-center">
            {/* Dial Code Display */}
            <span className="text-sm text-muted-foreground select-none">
              {dialCode}
            </span>

            {/* Phone Number Input */}
            <input
              ref={inputRef}
              type="tel"
              value={phoneNumber}
              onChange={handlePhoneChange}
              placeholder={placeholder}
              disabled={disabled}
              name={name}
              id={id}
              autoComplete={autoComplete}
              className="flex-1 bg-transparent border-0 outline-none text-sm text-foreground placeholder:text-muted-foreground disabled:opacity-50 disabled:cursor-not-allowed px-3 py-2"
            />
          </div>
        </div>

        {/* Error message */}
        {error && (
          <p className="mt-1 text-sm text-destructive">{error}</p>
        )}

        {/* Helper text */}
        {helperText && !error && (
          <p className="mt-1 text-sm text-muted-foreground">{helperText}</p>
        )}
      </div>
    );
  }
);

PhoneInputWithCountry.displayName = 'PhoneInputWithCountry';

export default PhoneInputWithCountry;
