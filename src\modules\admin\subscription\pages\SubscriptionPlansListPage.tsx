import React, { use<PERSON>emo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, ActionMenuItem, Chip } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import {
  useGetSubscriptionPlans,
  useCreateSubscriptionPlan,
  useUpdateSubscriptionPlan,
} from '../hooks/useSubscriptionPlansAdmin';
import {
  SubscriptionPlan,
  GetSubscriptionPlansQueryDto,
  CreateSubscriptionPlanDto,
  UpdateSubscriptionPlanDto,
  PackageType,
} from '../types/subscription-plans.admin.types';
import SubscriptionPlanForm from '../components/forms/SubscriptionPlanForm';
import SubscriptionPlanDetailForm from '../components/forms/SubscriptionPlanDetailForm';
import DeleteSubscriptionPlanModal from '../components/modals/DeleteSubscriptionPlanModal';
import { SubscriptionPlanEditForm } from '../components';

/**
 * Trang quản lý gói dịch vụ subscription sử dụng các hooks tối ưu
 */
const SubscriptionPlansListPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho form và modal
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form chỉnh sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form chi tiết
  const {
    isVisible: isDetailFormVisible,
    showForm: showDetailForm,
    hideForm: hideDetailForm,
  } = useSlideForm();

  // Xử lý xem chi tiết
  const handleViewDetail = useCallback(
    (plan: SubscriptionPlan) => {
      setSelectedPlan(plan);
      showDetailForm();
    },
    [showDetailForm]
  );

  // Xử lý chỉnh sửa
  const handleEdit = useCallback(
    (plan: SubscriptionPlan) => {
      setSelectedPlan(plan);
      showEditForm();
    },
    [showEditForm]
  );

  // Xử lý xóa
  const handleDelete = useCallback((plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
    setIsDeleteModalOpen(true);
  }, []);

  // Hàm render package type chip
  const renderPackageType = useCallback((packageType: PackageType) => {
    const typeConfig = {
      [PackageType.TIME_ONLY]: {
        label: t('admin:subscription.packageType.timeOnly'),
        variant: 'primary' as const,
      },
      [PackageType.HYBRID]: {
        label: t('admin:subscription.packageType.hybrid'),
        variant: 'info' as const,
      },
    };

    const config = typeConfig[packageType];
    return (
      <Chip
        variant={config.variant}
        size="sm"
      >
        {config.label}
      </Chip>
    );
  }, [t]);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<SubscriptionPlan>[]>(
    () => [
      {
        key: 'id',
        title: 'ID',
        dataIndex: 'id',
        width: '8%',
        sortable: true
      },
      {
        key: 'name',
        title: t('admin:subscription.plan.table.name'),
        dataIndex: 'name',
        width: '20%',
        sortable: true,
      },
      {
        key: 'description',
        title: t('admin:subscription.plan.table.description'),
        dataIndex: 'description',
        width: '30%',
        sortable: true,
        render: (value: unknown) => {
          const description = value as string;
          return (
            <div className="max-w-xs truncate" title={description}>
              {description}
            </div>
          );
        },
      },
      {
        key: 'packageType',
        title: t('admin:subscription.plan.table.packageType'),
        dataIndex: 'packageType',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          return renderPackageType(value as PackageType);
        },
      },
      {
        key: 'createdAt',
        title: t('admin:subscription.plan.table.createdAt'),
        dataIndex: 'createdAt',
        width: '12%',
        sortable: true,
        render: (value: unknown) => {
          const timestamp = value as string;
          const date = new Date(parseInt(timestamp));
          return (
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {date.toLocaleDateString('vi-VN')}
            </div>
          );
        },
      },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '15%',
        render: (_: unknown, record: SubscriptionPlan) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('subscription:plan.actions.viewDetail'),
              icon: 'eye',
              onClick: () => handleViewDetail(record),
            },
            {
              id: 'edit',
              label: t('common:edit'),
              icon: 'edit',
              onClick: () => handleEdit(record),
            },
            {
              id: 'delete',
              label: t('common:delete'),
              icon: 'trash',
              onClick: () => handleDelete(record),
            },
          ];

          return (
            <div className="flex justify-center">
              <ActionMenu
                items={actionItems}
                menuTooltip={t('common:moreActions', 'Thêm hành động')}
                iconSize="sm"
                iconVariant="default"
                placement="bottom"
                menuWidth="180px"
                menuIcon="more-horizontal"
                showAllInMenu={true}
                preferRight={true}
                preferTop={true}
              />
            </div>
          );
        },
      },
    ],
    [t, handleViewDetail, handleEdit, handleDelete, renderPackageType]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      {
        id: 'timeOnly',
        label: t('admin:subscription.packageType.timeOnly'),
        icon: 'clock',
        value: PackageType.TIME_ONLY
      },
      {
        id: 'hybrid',
        label: t('admin:subscription.packageType.hybrid'),
        icon: 'layers',
        value: PackageType.HYBRID
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): GetSubscriptionPlansQueryDto => {
    const queryParams: GetSubscriptionPlansQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue !== 'all') {
      queryParams.packageType = params.filterValue as PackageType;
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<SubscriptionPlan, GetSubscriptionPlansQueryDto>({
      columns,
      filterOptions,
      showDateFilter: true,
      createQueryParams,
    })
  );

  // Mutation để tạo plan mới
  const { mutateAsync: createPlan, isPending: isCreating } = useCreateSubscriptionPlan();

  // Mutation để cập nhật plan
  const { mutateAsync: updatePlan, isPending: isUpdating } = useUpdateSubscriptionPlan();

  // Gọi API lấy danh sách plans với queryParams từ dataTable
  const { data: planData, isLoading } = useGetSubscriptionPlans(dataTable.queryParams);

  // Xử lý thêm mới
  const handleAdd = () => {
    showAddForm();
  };

  // Add interface for form data
  interface SubscriptionPlanFormData {
    name: string;
    description: string;
    packageType: PackageType;
  }

  // Xử lý submit form thêm mới
  const handleSubmit = async (values: SubscriptionPlanFormData) => {
    if (isCreating) return;
    try {
      // Chuyển đổi values thành CreateSubscriptionPlanDto
      const createPlanDto: CreateSubscriptionPlanDto = {
        name: values['name'] as string,
        description: values['description'] as string,
        packageType: values['packageType'] as PackageType,
      };

      // Gọi API tạo plan mới
      await createPlan(createPlanDto);

      // Đóng form sau khi tạo thành công
      hideAddForm();
    } catch (error) {
      console.error(t('subscription:plan.actions.createError'), error);
    }
  };

  // Xử lý submit form chỉnh sửa
  const handleUpdateSubmit = async (id: number, data: UpdateSubscriptionPlanDto) => {
    try {
      // Gọi API cập nhật plan
      await updatePlan({ id, data });

      // Đóng form sau khi cập nhật thành công
      hideEditForm();

      // Trì hoãn việc set selectedPlan về null để đợi hiệu ứng đóng form hoàn tất
      setTimeout(() => {
        setSelectedPlan(null);
      }, 300);
    } catch (error) {
      console.error(t('subscription:plan.actions.updateError'), error);
    }
  };

  // Xử lý xóa thành công
  const handleDeleteSuccess = () => {
    console.log(t('subscription:plan.actions.deleteSuccess'));
    setSelectedPlan(null);
  };

  // Xử lý hủy form thêm mới
  const handleCancel = () => {
    hideAddForm();
  };

  // Xử lý hủy form chỉnh sửa
  const handleEditCancel = () => {
    hideEditForm();
    // Trì hoãn việc set selectedPlan về null để đợi hiệu ứng đóng form hoàn tất
    setTimeout(() => {
      setSelectedPlan(null);
    }, 300);
  };

  // Xử lý đóng form chi tiết
  const handleDetailClose = () => {
    hideDetailForm();
    // Trì hoãn việc set selectedPlan về null để đợi hiệu ứng đóng form hoàn tất
    setTimeout(() => {
      setSelectedPlan(null);
    }, 300);
  };

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [PackageType.TIME_ONLY]: t('admin:subscription.packageType.timeOnly'),
      [PackageType.HYBRID]: t('admin:subscription.packageType.hybrid'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form thêm mới */}
      <SlideInForm isVisible={isAddFormVisible}>
        <SubscriptionPlanForm
          onSubmit={handleSubmit} // Sử dụng handleSubmit mới để tránh trùng lặp API khi mở form nhiều lần liên tục
          onCancel={handleCancel}
          isSubmitting={isCreating}
        />
      </SlideInForm>

      {/* Form chỉnh sửa */}
      <SlideInForm isVisible={isEditFormVisible}>
        {selectedPlan && (
          <SubscriptionPlanEditForm
            plan={selectedPlan}
            onSubmit={handleUpdateSubmit}
            onCancel={handleEditCancel}
            isSubmitting={isUpdating}
          />
        )}
      </SlideInForm>

      {/* Form chi tiết */}
      <SlideInForm isVisible={isDetailFormVisible}>
        {selectedPlan && (
          <SubscriptionPlanDetailForm
            planId={selectedPlan.id}
            onClose={handleDetailClose}
          />
        )}
      </SlideInForm>

      {/* Modal xác nhận xóa */}
      {selectedPlan && (
        <DeleteSubscriptionPlanModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          plan={selectedPlan}
          onSuccess={handleDeleteSuccess}
        />
      )}

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={planData?.result?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: planData?.result?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: planData?.result?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default SubscriptionPlansListPage;
