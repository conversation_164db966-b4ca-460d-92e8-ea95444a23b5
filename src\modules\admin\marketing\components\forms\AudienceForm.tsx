import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Typography,
  TagsSelectWithPagination,
  PhoneInputWithCountry,
  CollapsibleCard,
  GenericCustomFieldSelector,
} from '@/shared/components/common';
import CustomFieldRenderer from '@/modules/business/components/CustomFieldRenderer';
import { useMarketingCustomFields } from '@/modules/marketing/hooks/useMarketingCustomFieldQuery';
import { MarketingCustomFieldBusinessService } from '@/modules/marketing/services/marketing-custom-field.service';
import {
  MarketingCustomFieldDataType,
  MarketingCustomFieldConfig,
  MarketingCustomFieldResponse
} from '@/modules/marketing/types/custom-field.types';
import { audienceSchema } from '../../schemas/audience.schema';
import { Audience } from '../../types/audience.types';

// Interface cho trường tùy chỉnh đã chọn với giá trị - mở rộng từ MarketingCustomFieldResponse
interface SelectedCustomField {
  id: number;
  fieldId: number; // Để tương thích với CustomFieldRenderer
  fieldKey: string;
  userId: number;
  displayName: string;
  dataType: MarketingCustomFieldDataType;
  description?: string;
  tags: string[];
  config: MarketingCustomFieldConfig;
  value: Record<string, unknown>;
  // Legacy properties for compatibility
  label?: string;
  component?: string;
  type?: string;
  required?: boolean;
  configJson?: Record<string, unknown>;
}

interface AudienceFormProps {
  /**
   * Dữ liệu audience ban đầu (nếu là chỉnh sửa)
   */
  initialData?: Audience;

  /**
   * Callback khi submit form
   */
  onSubmit: (values: Record<string, unknown>) => void;

  /**
   * Callback khi hủy
   */
  onCancel: () => void;

  /**
   * Trạng thái loading
   */
  isLoading?: boolean;
}

/**
 * Form tạo/chỉnh sửa audience
 */
const AudienceForm: React.FC<AudienceFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const { t } = useTranslation();
  const [selectedTags, setSelectedTags] = useState<number[]>([]);
  const [phoneValue, setPhoneValue] = useState<string>('');
  const [countryCode, setCountryCode] = useState<string>('+84');
  const [audienceCustomFields, setAudienceCustomFields] = useState<SelectedCustomField[]>([]);

  // Fetch marketing custom fields
  const { data: customFieldsData } = useMarketingCustomFields({});
  const customFields = customFieldsData?.items || [];

  // Create search function for GenericCustomFieldSelector - GỌI API THỰC SỰ
  const searchFunction = useCallback(async (params: { search?: string; page?: number; limit?: number }) => {
    console.log('🔍 AudienceForm searchFunction called with params:', params);

    try {
      const apiParams = {
        search: params.search,
        page: params.page || 1,
        limit: params.limit || 20,
        sortBy: 'id',
        sortDirection: 'ASC' as const,
      };

      console.log('📡 Calling MarketingCustomFieldBusinessService with:', apiParams);
      const response = await MarketingCustomFieldBusinessService.getCustomFieldsWithBusinessLogic(apiParams);
      console.log('✅ API response:', response);

      // Transform data for GenericCustomFieldSelector
      const items = response.result?.items || [];
      const meta = response.result?.meta || {};

      const transformedResult = {
        items: items.map((item: MarketingCustomFieldResponse) => ({
          id: item.id,
          label: item.displayName,
          dataType: item.dataType,
          type: item.dataType,
        })),
        totalItems: meta.totalItems || 0,
        totalPages: meta.totalPages || 0,
        currentPage: meta.currentPage || 1,
        hasNextPage: (meta.currentPage || 1) < (meta.totalPages || 1),
      };

      console.log('🔄 Transformed result:', transformedResult);
      return transformedResult;
    } catch (error) {
      console.error('❌ Error in AudienceForm search:', error);
      throw error;
    }
  }, []);

  // Initialize data from initialData
  useEffect(() => {
    if (initialData) {
      if (initialData.tagIds) {
        setSelectedTags(initialData.tagIds);
      }
      if (initialData.phone) {
        setPhoneValue(initialData.phone);
      }
      // Có thể thêm logic để parse countryCode từ initialData nếu cần

      // Initialize custom fields if they exist
      if (initialData.attributes && initialData.attributes.length > 0) {
        const customFields: SelectedCustomField[] = initialData.attributes.map((attr, index) => ({
          id: Date.now() + index,
          fieldId: parseInt(attr.id) || Date.now() + index,
          fieldKey: `field_${attr.id}`,
          userId: 1, // Default user ID
          displayName: attr.name,
          dataType: MarketingCustomFieldDataType.TEXT,
          description: '',
          tags: [],
          config: {},
          value: { value: attr.value },
          // Legacy properties for compatibility
          label: attr.name,
          component: 'input',
          type: 'text',
          required: false,
          configJson: {},
        }));
        setAudienceCustomFields(customFields);
      }
    }
  }, [initialData]);

  const handlePhoneChange = (internationalPhone: string) => {
    setPhoneValue(internationalPhone);
    // Extract country code from international phone number
    // For now, we'll set a default country code
    setCountryCode('+84'); // Default to Vietnam
  };

  // Thêm/xóa trường tùy chỉnh vào audience
  const handleToggleCustomFieldToProduct = useCallback(
    (fieldId: number) => {
      setAudienceCustomFields(prev => {
        const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          return prev.filter((_, index) => index !== existingFieldIndex);
        }

        // Tìm custom field từ danh sách để lấy thông tin đầy đủ
        const customField = customFields.find(field => field.id === fieldId);
        if (!customField) {
          console.warn(`Custom field with id ${fieldId} not found`);
          return prev;
        }

        // Thêm trường mới với cấu trúc MarketingCustomFieldResponse + fieldId + value
        const newField: SelectedCustomField = {
          ...customField,
          fieldId: customField.id, // Để tương thích với CustomFieldRenderer
          value: { value: '' }, // Giá trị mặc định
        };

        return [...prev, newField];
      });
    },
    [customFields]
  );

  // Xóa trường tùy chỉnh khỏi audience
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setAudienceCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong audience
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string | number | boolean) => {
    setAudienceCustomFields(prev =>
      prev.map(field => {
        if (field.id === customFieldId) {
          return {
            ...field,
            value: { value },
          };
        }
        return field;
      })
    );
  }, []);

  // Xử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    // Thêm selectedTags, phone, countryCode và customFields vào values
    const formData = {
      ...values,
      phone: phoneValue,
      countryCode: countryCode,
      tagIds: selectedTags,
      customFields: audienceCustomFields.map(field => ({
        fieldId: field.fieldId,
        configId: field.configJson?.['configId'] || field.fieldId.toString(),
        value: field.value['value'],
      })),
    };
    onSubmit(formData);
  };

  // Giá trị mặc định cho form
  const defaultValues = initialData
    ? {
        name: initialData.name,
        email: initialData.email || '',
      }
    : {};

  return (
    <Card className="mb-4 p-4">
      <Typography variant="h5" className="mb-4">
        {initialData
          ? t('audience.edit', 'Chỉnh sửa đối tượng')
          : t('audience.addNew', 'Thêm đối tượng mới')}
      </Typography>

      <Form
        schema={audienceSchema}
        onSubmit={handleSubmit}
        className="space-y-4"
        defaultValues={defaultValues}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem name="name" label={t('audience.name', 'Tên đối tượng')} required>
            <Input placeholder={t('audience.name.placeholder', 'Nhập tên đối tượng')} fullWidth />
          </FormItem>

          <FormItem name="email" label={t('audience.email', 'Email')} required>
            <Input type="email" placeholder={t('audience.email.placeholder', 'Nhập email')} fullWidth />
          </FormItem>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              {t('audience.phone', 'Số điện thoại')} <span className="text-red-500">*</span>
            </label>
            <PhoneInputWithCountry
              value={phoneValue}
              onChange={handlePhoneChange}
              placeholder={t('audience.phone.placeholder', 'Nhập số điện thoại')}
              fullWidth
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              {t('audience.tags', 'Tags')}
            </label>
            <TagsSelectWithPagination
              value={selectedTags}
              onChange={setSelectedTags}
              placeholder={t('audience.tags.placeholder', 'Chọn tags...')}
              fullWidth
            />
          </div>
        </div>

        {/* 7. Trường tùy chỉnh */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.customFields', '7. Trường tùy chỉnh')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <GenericCustomFieldSelector
              onFieldSelect={(fieldData) => {
                handleToggleCustomFieldToProduct(fieldData.id);
              }}
              selectedFieldIds={audienceCustomFields.map(f => f.fieldId)}
              placeholder={t(
                'marketing:audience.customFields.searchPlaceholder',
                'Nhập từ khóa và nhấn Enter để tìm trường tùy chỉnh...'
              )}
              searchFunction={searchFunction}
              title={t('marketing:customField.title', 'Trường tùy chỉnh')}
              translationNamespace="marketing"
            />

            {audienceCustomFields.length > 0 && (
              <div className="space-y-3">
                {audienceCustomFields.map(field => {
                  // Adapter để chuyển đổi cấu trúc mới sang cấu trúc cũ cho CustomFieldRenderer
                  const adaptedField = {
                    id: field.id,
                    fieldId: field.fieldId,
                    label: field.displayName,
                    component: field.dataType as string,
                    type: field.dataType as string,
                    required: false,
                    configJson: field.config as Record<string, unknown>,
                    value: field.value,
                  };

                  return (
                    <CustomFieldRenderer
                      key={field.id}
                      field={adaptedField}
                      value={String(field.value['value'] ?? '')}
                      onChange={value => handleUpdateCustomFieldInProduct(field.id, value)}
                      onRemove={() => handleRemoveCustomFieldFromProduct(field.id)}
                    />
                  );
                })}
              </div>
            )}
          </div>
        </CollapsibleCard>

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={onCancel} disabled={isLoading}>
            {t('common.cancel', 'Hủy')}
          </Button>
          <Button type="submit" isLoading={isLoading}>
            {initialData ? t('common.update', 'Cập nhật') : t('common.create', 'Tạo mới')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default AudienceForm;
