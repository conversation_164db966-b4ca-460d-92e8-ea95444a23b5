import React from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { SubmitHandler, FieldValues } from 'react-hook-form';
import {
  Card,
  Typography,
  Form,
  FormItem,
  Input,
  Grid,
  IconCard,
} from '@/shared/components/common';
import { ShopInfoDto } from '../../types/shop.types';

// Schema validation cho form
const shopInfoSchema = z.object({
  shopName: z.string().min(1, 'Tên cửa hàng là bắt buộc'),
  shopPhone: z.string().min(1, 'Số điện thoại là bắt buộc'),
  shopAddress: z.string().min(1, 'Địa chỉ là bắt buộc'),
  shopProvince: z.string().min(1, 'Tỉnh/Thành phố là bắt buộc'),
  shopDistrict: z.string().min(1, 'Quận/Huyện là bắt buộc'),
  shopWard: z.string().min(1, 'Phường/Xã là bắt buộc'),
});

export type ShopInfoFormData = z.infer<typeof shopInfoSchema>;

export interface ShopInfoFormProps {
  shopInfo?: ShopInfoDto;
  onSubmit: SubmitHandler<ShopInfoFormData>;
  onCancel: () => void;
  isSubmitting: boolean;
}

/**
 * Component form tạo/cập nhật thông tin cửa hàng
 */
const ShopInfoForm: React.FC<ShopInfoFormProps> = ({ 
  shopInfo, 
  onSubmit, 
  onCancel, 
  isSubmitting 
}) => {
  const { t } = useTranslation('business');

  // Default values cho form
  const defaultValues: ShopInfoFormData = {
    shopName: shopInfo?.shopName || '',
    shopPhone: shopInfo?.shopPhone || '',
    shopAddress: shopInfo?.shopAddress || '',
    shopProvince: shopInfo?.shopProvince || '',
    shopDistrict: shopInfo?.shopDistrict || '',
    shopWard: shopInfo?.shopWard || '',
  };

  return (
    <Card className="p-6">
      <Typography variant="h3" className="mb-6">
        {shopInfo 
          ? t('business:shop.form.editTitle') 
          : t('business:shop.form.createTitle')
        }
      </Typography>

      <Form
        schema={shopInfoSchema}
        onSubmit={onSubmit as SubmitHandler<FieldValues>}
        defaultValues={defaultValues}
        key={shopInfo?.id || 'new'}
      >
        <Grid columns={{ xs: 1, md: 1 }} columnGap="md" rowGap="md">
          <FormItem name="shopName" label={t('business:shop.form.shopName')} required>
            <Input placeholder={t('business:shop.form.shopNamePlaceholder')} fullWidth/>
          </FormItem>

          <FormItem name="shopPhone" label={t('business:shop.form.shopPhone')} required>
            <Input placeholder={t('business:shop.form.shopPhonePlaceholder')} fullWidth/>
          </FormItem>

          <FormItem name="shopAddress" label={t('business:shop.form.shopAddress')} required>
            <Input placeholder={t('business:shop.form.shopAddressPlaceholder')} fullWidth/>
          </FormItem>

          <FormItem name="shopProvince" label={t('business:shop.form.shopProvince')} required>
            <Input placeholder={t('business:shop.form.shopProvincePlaceholder')} fullWidth/>
          </FormItem>

          <FormItem name="shopDistrict" label={t('business:shop.form.shopDistrict')} required>
            <Input placeholder={t('business:shop.form.shopDistrictPlaceholder')} fullWidth/>
          </FormItem>

          <FormItem name="shopWard" label={t('business:shop.form.shopWard')} required>
            <Input placeholder={t('business:shop.form.shopWardPlaceholder')} fullWidth/>
          </FormItem>
        </Grid>

        {/* Action buttons */}
        <div className="flex justify-end space-x-3 mt-6">
          <IconCard
            icon="x"
            title={t('business:common.actions.cancel')}
            onClick={onCancel}
            variant="secondary"
            disabled={isSubmitting}
            className="cursor-pointer"
          />

          <IconCard
            icon="save"
            title={shopInfo
              ? t('business:common.actions.save')
              : t('business:common.actions.create')
            }
            onClick={() => {
              // Trigger form submit
              const form = document.querySelector('form');
              if (form) {
                form.requestSubmit();
              }
            }}
            variant="primary"
            disabled={isSubmitting}
            isLoading={isSubmitting}
            className="cursor-pointer"
          />
        </div>
      </Form>
    </Card>
  );
};

export default ShopInfoForm;
