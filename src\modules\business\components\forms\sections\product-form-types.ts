import { SelectedCustomField } from '@/modules/business/types/custom-field.types';
import { FileWithMetadata } from '@/shared/types/file.types';

// Interface cho inventory trong form
export interface ProductInventory {
  id: number; // ID tạm thời cho UI
  warehouseId?: string | number;
  availableQuantity?: string | number;
  sku?: string;
  barcode?: string;
}

// Interface cho biến thể sản phẩm trong form
export interface ProductVariant {
  id: number; // ID tạm thời cho UI
  name: string;
  listPrice: string | number;
  salePrice: string | number;
  currency: string;
  priceDescription?: string;
  sku?: string;
  availableQuantity?: string | number;
  customFields: SelectedCustomField[];
  images?: ExtendedFileWithMetadata[];
}

// Interface mở rộng cho file metadata
export interface ExtendedFileWithMetadata extends FileWithMetadata {
  url?: string;
  name?: string;
  size?: number;
  type?: string;
}

// Interface cho form values
export interface ProductFormValues {
  name: string;
  listPrice: string | number;
  salePrice: string | number;
  currency: string;
  description?: string;
  tags?: string[];
  shipmentConfig?: {
    lengthCm?: string | number;
    widthCm?: string | number;
    heightCm?: string | number;
    weightGram?: string | number;
  };
  inventory?: ProductInventory[];
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  classifications?: ProductVariant[]; // Đổi tên từ variants thành classifications
}

// Props cho các section components
export interface SectionProps {
  t: (key: string, fallback?: string) => string;
}

export interface GeneralInfoSectionProps extends SectionProps {}

export interface PricingSectionProps extends SectionProps {}

export interface MediaSectionProps extends SectionProps {
  mediaFiles: FileWithMetadata[];
  setMediaFiles: (files: FileWithMetadata[]) => void;
  tempTags: string[];
  setTempTags: (tags: string[]) => void;
}

export interface InventorySectionProps extends SectionProps {
  productInventories: ProductInventory[];
  setProductInventories: (inventories: ProductInventory[]) => void;
  handleAddInventory: () => void;
  handleRemoveInventory: (inventoryId: number) => void;
  handleUpdateInventory: (inventoryId: number, field: keyof ProductInventory, value: string | number) => void;
}

export interface ShippingSectionProps extends SectionProps {}

export interface ClassificationsSectionProps extends SectionProps {
  productClassifications: ProductVariant[];
  setProductClassifications: (classifications: ProductVariant[]) => void;
  handleAddVariant: () => void;
  handleRemoveVariant: (variantId: number) => void;
  handleUpdateVariant: (variantId: number, field: keyof ProductVariant, value: string | number) => void;
  handleVariantImagesChange: (variantId: number, images: ExtendedFileWithMetadata[]) => void;
  handleToggleCustomFieldToVariant: (variantId: number, fieldId: number, fieldData?: Record<string, unknown>) => void;
  handleUpdateCustomFieldInVariant: (variantId: number, customFieldId: number, value: string) => void;
  handleRemoveCustomFieldFromVariant: (variantId: number, customFieldId: number) => void;
}

export interface CustomFieldsSectionProps extends SectionProps {
  productCustomFields: SelectedCustomField[];
  setProductCustomFields: (fields: SelectedCustomField[]) => void;
  handleToggleCustomFieldToProduct: (fieldId: number, fieldData?: Record<string, unknown>) => void;
  handleUpdateCustomFieldInProduct: (customFieldId: number, value: string) => void;
  handleRemoveCustomFieldFromProduct: (customFieldId: number) => void;
}
