import { SelectedCustomField } from '@/modules/business/types/custom-field.types';
import { FileWithMetadata } from '@/shared/types/file.types';

// Re-export types from ProductForm to avoid duplication
export type { ProductInventory, ProductVariant, ExtendedFileWithMetadata } from '../ProductForm';

// Interface cho form values
export interface ProductFormValues {
  name: string;
  listPrice: string | number;
  salePrice: string | number;
  currency: string;
  description?: string;
  tags?: string[];
  shipmentConfig?: {
    lengthCm?: string | number;
    widthCm?: string | number;
    heightCm?: string | number;
    weightGram?: string | number;
  };
  inventory?: ProductInventory[];
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  classifications?: ProductVariant[]; // Đổi tên từ variants thành classifications
}

// Props cho các section components
export interface SectionProps {
  t: (key: string, fallback?: string) => string;
}

export interface GeneralInfoSectionProps {
  tempTags: string[];
  setTempTags: (tags: string[]) => void;
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface PricingSectionProps {}

export interface MediaSectionProps {
  mediaFiles: FileWithMetadata[];
  setMediaFiles: (files: FileWithMetadata[]) => void;
  tempTags: string[];
  setTempTags: (tags: string[]) => void;
}

export interface InventorySectionProps {
  productInventories: ProductInventory[];
  setProductInventories: (inventories: ProductInventory[]) => void;
  handleAddInventory: () => void;
  handleRemoveInventory: (inventoryId: number) => void;
  handleUpdateInventory: (inventoryId: number, field: string | number | symbol, value: string | number) => void;
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface ShippingSectionProps {}

export interface ClassificationsSectionProps {
  productClassifications: ProductVariant[];
  setProductClassifications: (classifications: ProductVariant[]) => void;
  handleAddVariant: () => void;
  handleRemoveVariant: (variantId: number) => void;
  handleUpdateVariant: (variantId: number, field: string | number | symbol, value: string | number) => void;
  handleVariantImagesChange: (variantId: number, images: ExtendedFileWithMetadata[]) => void;
  handleToggleCustomFieldToVariant: (variantId: number, fieldId: number, fieldData?: Record<string, unknown>) => void;
  handleUpdateCustomFieldInVariant: (variantId: number, customFieldId: number, value: string) => void;
  handleRemoveCustomFieldFromVariant: (variantId: number, customFieldId: number) => void;
}

export interface CustomFieldsSectionProps {
  productCustomFields: SelectedCustomField[];
  setProductCustomFields: (fields: SelectedCustomField[]) => void;
  handleToggleCustomFieldToProduct: (fieldId: number, fieldData?: Record<string, unknown>) => void;
  handleUpdateCustomFieldInProduct: (customFieldId: number, value: string) => void;
  handleRemoveCustomFieldFromProduct: (customFieldId: number) => void;
}

// Interface cho form values
export interface ProductFormValues {
  name: string;
  listPrice: string | number;
  salePrice: string | number;
  currency: string;
  description?: string;
  tags?: string[];
  shipmentConfig?: {
    lengthCm?: string | number;
    widthCm?: string | number;
    heightCm?: string | number;
    weightGram?: string | number;
  };
  inventory?: ProductInventory[];
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  classifications?: ProductVariant[]; // Đổi tên từ variants thành classifications
}

// Props cho các section components
export interface SectionProps {
  t: (key: string, fallback?: string) => string;
}

export interface GeneralInfoSectionProps extends SectionProps {}

export interface PricingSectionProps extends SectionProps {}

export interface MediaSectionProps {
  mediaFiles: FileWithMetadata[];
  setMediaFiles: (files: FileWithMetadata[]) => void;
  tempTags: string[];
  setTempTags: (tags: string[]) => void;
}

export interface InventorySectionProps {
  productInventories: ProductInventory[];
  setProductInventories: (inventories: ProductInventory[]) => void;
  handleAddInventory: () => void;
  handleRemoveInventory: (inventoryId: number) => void;
  handleUpdateInventory: (inventoryId: number, field: keyof ProductInventory, value: string | number) => void;
}

export interface ShippingSectionProps extends SectionProps {}

export interface ClassificationsSectionProps {
  productClassifications: ProductVariant[];
  setProductClassifications: (classifications: ProductVariant[]) => void;
  handleAddVariant: () => void;
  handleRemoveVariant: (variantId: number) => void;
  handleUpdateVariant: (variantId: number, field: keyof ProductVariant, value: string | number) => void;
  handleVariantImagesChange: (variantId: number, images: ExtendedFileWithMetadata[]) => void;
  handleToggleCustomFieldToVariant: (variantId: number, fieldId: number, fieldData?: Record<string, unknown>) => void;
  handleUpdateCustomFieldInVariant: (variantId: number, customFieldId: number, value: string) => void;
  handleRemoveCustomFieldFromVariant: (variantId: number, customFieldId: number) => void;
}

export interface CustomFieldsSectionProps {
  productCustomFields: SelectedCustomField[];
  setProductCustomFields: (fields: SelectedCustomField[]) => void;
  handleToggleCustomFieldToProduct: (fieldId: number, fieldData?: Record<string, unknown>) => void;
  handleUpdateCustomFieldInProduct: (customFieldId: number, value: string) => void;
  handleRemoveCustomFieldFromProduct: (customFieldId: number) => void;
}
