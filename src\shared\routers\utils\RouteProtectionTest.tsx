import React from 'react';
import { useAuth<PERSON><PERSON>mon, AuthType } from '@/shared/hooks/useAuthCommon';
import { useLocation, useNavigate } from 'react-router-dom';
import { Typo<PERSON>, <PERSON><PERSON>, Card } from '@/shared/components/common';

/**
 * Component test để kiểm tra route protection
 * Chỉ dùng cho development/testing
 */
const RouteProtectionTest: React.FC = () => {
  const { 
    isTokenValid, 
    authType, 
    isAuthenticated, 
    isUserAuthenticated, 
    isAdminAuthenticated,
    accessToken,
    expiresAt,
    clearAuth 
  } = useAuthCommon();
  
  const location = useLocation();
  const navigate = useNavigate();

  const handleClearAuth = () => {
    clearAuth();
    console.log('Auth cleared');
  };

  const handleNavigateToUser = () => {
    navigate('/data');
  };

  const handleNavigateToAdmin = () => {
    navigate('/admin');
  };

  const handleNavigateToAuth = () => {
    navigate('/auth');
  };

  const handleNavigateToAdminAuth = () => {
    navigate('/admin/auth');
  };

  return (
    <div className="w-full bg-background text-foreground p-6">
      <Card className="max-w-4xl mx-auto">
        <div className="p-6 space-y-6">
          <Typography variant="h1" className="text-center">
            Route Protection Test
          </Typography>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Auth Status */}
            <div className="space-y-4">
              <Typography variant="h3">Auth Status</Typography>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Current Path:</span>
                  <span className="font-mono">{location.pathname}</span>
                </div>
                <div className="flex justify-between">
                  <span>Is Authenticated:</span>
                  <span className={isAuthenticated ? 'text-green-600' : 'text-red-600'}>
                    {isAuthenticated ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Auth Type:</span>
                  <span className="font-mono">{authType || 'None'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Is User Auth:</span>
                  <span className={isUserAuthenticated ? 'text-green-600' : 'text-red-600'}>
                    {isUserAuthenticated ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Is Admin Auth:</span>
                  <span className={isAdminAuthenticated ? 'text-green-600' : 'text-red-600'}>
                    {isAdminAuthenticated ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Token Valid:</span>
                  <span className={isTokenValid() ? 'text-green-600' : 'text-red-600'}>
                    {isTokenValid() ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Has Token:</span>
                  <span className={accessToken ? 'text-green-600' : 'text-red-600'}>
                    {accessToken ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Expires At:</span>
                  <span className="font-mono text-sm">
                    {expiresAt ? new Date(expiresAt).toLocaleString() : 'N/A'}
                  </span>
                </div>
              </div>
            </div>

            {/* Navigation Tests */}
            <div className="space-y-4">
              <Typography variant="h3">Navigation Tests</Typography>
              <div className="space-y-2">
                <Button 
                  onClick={handleNavigateToAuth}
                  variant="outline"
                  className="w-full"
                >
                  Go to User Auth (/auth)
                </Button>
                <Button 
                  onClick={handleNavigateToAdminAuth}
                  variant="outline"
                  className="w-full"
                >
                  Go to Admin Auth (/admin/auth)
                </Button>
                <Button 
                  onClick={handleNavigateToUser}
                  variant="primary"
                  className="w-full"
                >
                  Go to User Page (/data)
                </Button>
                <Button 
                  onClick={handleNavigateToAdmin}
                  variant="primary"
                  className="w-full"
                >
                  Go to Admin Page (/admin)
                </Button>
                <Button 
                  onClick={handleClearAuth}
                  variant="destructive"
                  className="w-full"
                >
                  Clear Auth & Test Redirect
                </Button>
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 bg-gray-100 rounded-lg">
            <Typography variant="body2" className="text-gray-600">
              <strong>Test Instructions:</strong><br/>
              1. Login as user or admin first<br/>
              2. Try navigating to different pages<br/>
              3. Use "Clear Auth" to test redirect behavior<br/>
              4. Check browser console for debug logs
            </Typography>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default RouteProtectionTest;
