import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Table,
  SlideInForm,
} from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { useSlideForm } from '@/shared/hooks/useSlideForm';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { FilterOption } from '@/shared/hooks/table/useFilterOptions';
import { useShopList, useCreateShopInfo, useUpdateShopInfo, useDeleteMultipleShops, ShopQueryParams } from '../hooks/useShopQuery';
import { ShopInfoDto } from '../types/shop.types';
import { formatTimestamp } from '@/shared/utils/date';
import { NotificationUtil } from '@/shared/utils/notification';
import ConfirmDeleteModal from '@/shared/components/common/ConfirmDeleteModal';
import ActionMenu, { ActionMenuItem } from '@/shared/components/common/ActionMenu';
import ShopInfoForm, { ShopInfoFormData } from '../components/forms/ShopInfoForm';





/**
 * Trang quản lý thông tin cửa hàng
 */
const ShopPage: React.FC = () => {
  const { t } = useTranslation('business');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingShop, setEditingShop] = useState<ShopInfoDto | undefined>(undefined);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // Hook quản lý SlideInForm
  const {
    isVisible: isFormVisible,
    showForm,
    hideForm,
  } = useSlideForm();

  // Hooks
  const createShopMutation = useCreateShopInfo();
  const updateShopMutation = useUpdateShopInfo();
  const deleteMultipleShopsMutation = useDeleteMultipleShops();

  // Xử lý chỉnh sửa cửa hàng
  const handleEditShop = useCallback((shop: ShopInfoDto) => {
    setEditingShop(shop);
    showForm();
  }, [showForm]);

  // Cấu hình columns cho table
  const columns = useMemo((): TableColumn<ShopInfoDto>[] => [
    {
      title: t('business:shop.form.shopId'),
      dataIndex: 'id',
      key: 'id',
      sortable: true,
    },
    {
      title: t('business:shop.form.shopName'),
      dataIndex: 'shopName',
      key: 'shopName',
      sortable: true,
    },
    {
      title: t('business:shop.form.shopPhone'),
      dataIndex: 'shopPhone',
      key: 'shopPhone',
    },
    {
      title: t('business:shop.form.shopAddress'),
      dataIndex: 'shopAddress',
      key: 'shopAddress',
    },
    {
      title: t('business:shop.form.shopProvince'),
      dataIndex: 'shopProvince',
      key: 'shopProvince',
    },
    {
      title: t('common:createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      sortable: true,
      render: (value: unknown) => formatTimestamp(Number(value)),
    },
    {
      title: t('common:actions'),
      key: 'actions',
      width: 80,
      render: (_: unknown, record: ShopInfoDto) => {
        // Tạo danh sách các action items
        const actionItems: ActionMenuItem[] = [
          {
            id: 'edit',
            label: t('common:edit'),
            icon: 'edit',
            onClick: () => handleEditShop(record),
          },
        ];

        return (
          <ActionMenu
            items={actionItems}
            menuTooltip={t('common:moreActions')}
            iconSize="sm"
            iconVariant="default"
            placement="bottom"
            menuWidth="180px"
            showAllInMenu={true}
            preferRight={true}
          />
        );
      },
    },
  ], [t, handleEditShop]);

  // Filter options for shops
  const filterOptions = useMemo((): FilterOption[] => [
    { id: 'all', label: t('common:all'), value: 'all' },
    { id: 'recent', label: t('business:shop.filter.recent'), value: 'recent' },
  ], [t]);

  // Create query params function
  const createQueryParams = useCallback((params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): ShopQueryParams => {
    const queryParams: ShopQueryParams = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    return queryParams;
  }, []);

  // Data table configuration
  const dataTableConfig = useDataTableConfig<ShopInfoDto, ShopQueryParams>({
    columns,
    filterOptions,
    showDateFilter: false,
    createQueryParams,
  });

  const dataTable = useDataTable(dataTableConfig);

  // Get shop list with current query params
  const { data: shopData, isLoading } = useShopList(dataTable.queryParams);

  // Handle sort change wrapper
  const handleSortChangeWrapper = useCallback((sortBy: string | null, sortDirection: SortOrder) => {
    dataTable.tableData.handleSortChange(sortBy, sortDirection);
  }, [dataTable.tableData]);

  // Use ActiveFilters hook
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      all: t('common:all'),
      recent: t('business:shop.filter.recent'),
    },
    t,
  });

  // Xử lý tạo cửa hàng mới
  const handleCreateShop = () => {
    setEditingShop(undefined);
    showForm();
  };

  // Xử lý submit form
  const handleSubmitShop = async (data: ShopInfoFormData) => {
    setIsSubmitting(true);
    try {
      // Kiểm tra xem có đang edit shop không
      const currentEditingShop = editingShop;

      // Ensure all required fields are present
      const shopData = {
        shopName: data.shopName,
        shopPhone: data.shopPhone,
        shopAddress: data.shopAddress,
        shopProvince: data.shopProvince,
        shopDistrict: data.shopDistrict,
        shopWard: data.shopWard,
      };

      if (currentEditingShop) {
        // Cập nhật cửa hàng hiện có
        await updateShopMutation.mutateAsync({
          id: currentEditingShop.id,
          data: shopData,
        });
      } else {
        // Tạo cửa hàng mới
        await createShopMutation.mutateAsync(shopData);
      }

      hideForm();
      setEditingShop(undefined);
    } catch (error) {
      console.error('Submit shop info error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý hủy form
  const handleCancelForm = () => {
    hideForm();
    setEditingShop(undefined);
  };

  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      NotificationUtil.warning({
        message: t('business:shop.selectToDelete'),
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      // Gọi API xóa nhiều cửa hàng cùng lúc
      await deleteMultipleShopsMutation.mutateAsync(selectedRowKeys.map(key => Number(key)));

      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:shop.messages.bulkDeleteSuccess', { count: selectedRowKeys.length }),
        duration: 3000,
      });
    } catch {
      NotificationUtil.error({
        message: t('business:shop.messages.bulkDeleteError'),
        duration: 3000,
      });
    }
  }, [selectedRowKeys, deleteMultipleShopsMutation, t]);

  return (
    <div className="w-full bg-background text-foreground">
      <MenuIconBar
        onAdd={handleCreateShop}
        onSearch={dataTable.tableData.handleSearch}
        items={dataTable.filter.menuItems}
        showDateFilter={false}
        showColumnFilter={true}
        columns={dataTable.columnVisibility.visibleColumns}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete'),
            variant: 'primary',
            onClick: handleShowBulkDeleteConfirm,
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* ActiveFilters component */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* SlideInForm cho tạo/cập nhật cửa hàng */}
      <SlideInForm isVisible={isFormVisible}>
        <ShopInfoForm
          {...(editingShop && { shopInfo: editingShop })} // Chỉ truyền shopInfo khi có giá trị
          onSubmit={handleSubmitShop}
          onCancel={handleCancelForm}
          isSubmitting={isSubmitting}
        />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={shopData || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: dataTable.tableData.currentPage,
            pageSize: dataTable.tableData.pageSize,
            total: shopData?.length || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete')}
        message={t('business:shop.confirmBulkDeleteMessage', { count: selectedRowKeys.length })}
      />
    </div>
  );
};

export default ShopPage;
