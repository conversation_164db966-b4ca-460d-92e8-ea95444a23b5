/**
 * Hook quản lý các tùy chọn lọc
 * @module useFilterOptions
 */
import { useState, useCallback, useMemo } from 'react';
import { ModernMenuItem } from '@/shared/components/common/ModernMenu';

/**
 * Interface cho tùy chọn lọc
 */
export interface FilterOption {
  /**
   * ID của tùy chọn
   */
  id: string;

  /**
   * Nhãn hiển thị
   */
  label: string;

  /**
   * Tên icon (tùy chọn)
   */
  icon?: string;

  /**
   * Giá trị thực tế (có thể khác với id)
   */
  value: string | number | boolean;
}

/**
 * Interface cho tham số đầu vào của hook useFilterOptions
 */
export interface UseFilterOptionsProps {
  /**
   * Danh sách các tùy chọn lọc
   */
  options: FilterOption[];

  /**
   * ID của tùy chọn mặc định
   * @default options[0].id
   */
  defaultSelectedId?: string;

  /**
   * Callback khi thay đổi lọc
   */
  onChange?: (value: string | number | boolean) => void;
}

/**
 * Hook quản lý các tùy chọn lọc
 * @param props Tham số đầu vào
 * @returns Các state và hàm xử lý cho tùy chọn lọc
 */
export function useFilterOptions(props: UseFilterOptionsProps) {
  const { options, defaultSelectedId, onChange } = props;

  // Lấy ID mặc định từ tùy chọn đầu tiên nếu không được cung cấp
  const initialSelectedId = defaultSelectedId || (options.length > 0 ? options[0]?.id : '') || '';

  // State cho ID tùy chọn đang được chọn
  const [selectedId, setSelectedId] = useState(initialSelectedId);

  // Lấy giá trị thực tế của tùy chọn đang được chọn
  const selectedValue = useMemo(() => {
    const selectedOption = options.find(option => option.id === selectedId);
    return selectedOption ? selectedOption.value : undefined;
  }, [selectedId, options]);

  // Xử lý thay đổi tùy chọn lọc
  const handleFilterChange = useCallback(
    (id: string) => {
      setSelectedId(id);

      // Tìm giá trị thực tế của tùy chọn được chọn
      const selectedOption = options.find(option => option.id === id);
      if (selectedOption && onChange) {
        onChange(selectedOption.value);
      }
    },
    [options, onChange]
  );

  // Tạo danh sách các item cho ModernMenu
  const menuItems = useMemo<ModernMenuItem[]>(() => {
    return options.map(option => ({
      id: option.id,
      label: option.label,
      icon: option.icon,
      onClick: () => handleFilterChange(option.id),
      active: selectedId === option.id,
      keepOpen: true, // Keep menu open for filter selections
    }));
  }, [options, selectedId, handleFilterChange]);

  return {
    // States
    selectedId,
    selectedValue,
    menuItems,

    // Handlers
    handleFilterChange,
    setSelectedId,
  };
}
