/**
 * Hook tổng hợp quản lý dữ liệu bảng với đầy đủ tính năng
 * @module useDataTable
 */
import { useMemo, useCallback, useState, useEffect } from 'react';
import { TableColumn } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useTableData, UseTableDataOptions } from './useTableData';
import { useColumnVisibility, UseColumnVisibilityOptions } from './useColumnVisibility';
import { useFilterOptions, UseFilterOptionsProps, FilterOption } from './useFilterOptions';
import { useDateRangeFilter, UseDateRangeFilterProps } from './useDateRangeFilter';
import { ModernMenuItem } from '@/shared/components/common/ModernMenu';

/**
 * Interface cho dữ liệu phân trang từ API
 */
export interface PaginatedData<T> {
  items: T[];
  meta: {
    totalItems: number;
    currentPage: number;
    totalPages: number;
    itemsPerPage: number;
  };
}

/**
 * Interface cho dữ liệu bảng
 */
export interface TableData<T> {
  items: T[];
  totalItems: number;
  currentPage: number;
  totalPages: number;
  loading: boolean;
}

/**
 * Interface cho tham số đầu vào của hook useDataTable
 */
export interface UseDataTableOptions<T, TQueryParams> {
  /**
   * Danh sách cột của bảng
   */
  columns: TableColumn<T>[];

  /**
   * Tùy chọn cho hook useTableData
   */
  tableDataOptions: Omit<UseTableDataOptions<TQueryParams>, 'createQueryParams'>;

  /**
   * Tùy chọn cho hook useColumnVisibility
   * @optional
   */
  columnVisibilityOptions?: Omit<UseColumnVisibilityOptions<T>, 'columns'>;

  /**
   * Tùy chọn cho hook useFilterOptions
   * @optional
   */
  filterOptions?: UseFilterOptionsProps;

  /**
   * Tùy chọn cho hook useDateRangeFilter
   * @optional
   */
  dateRangeOptions?: UseDateRangeFilterProps;

  /**
   * Hàm tạo query params từ tất cả các tham số
   */
  createQueryParams: (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }) => TQueryParams;

  /**
   * Dữ liệu phân trang từ API
   * @optional
   */
  apiData?: PaginatedData<T> | undefined;

  /**
   * Trạng thái đang tải
   * @optional
   * @default false
   */
  isLoading?: boolean;
}

/**
 * Hook tổng hợp quản lý dữ liệu bảng với đầy đủ tính năng
 * @template T Kiểu dữ liệu của dòng trong bảng
 * @template TQueryParams Kiểu dữ liệu của query params
 * @param options Tùy chọn cho hook
 * @returns Các state và hàm xử lý cho bảng
 */
export function useDataTable<T, TQueryParams>(options: UseDataTableOptions<T, TQueryParams>) {
  const {
    columns,
    tableDataOptions,
    columnVisibilityOptions,
    filterOptions,
    dateRangeOptions,
    createQueryParams,
    apiData,
    isLoading = false,
  } = options;

  // Sử dụng hook useFilterOptions
  const filter = useFilterOptions(
    filterOptions || {
      options: [],
      defaultSelectedId: '',
    }
  );

  // Sử dụng hook useDateRangeFilter
  const dateRange = useDateRangeFilter(dateRangeOptions);

  // Tạo hàm createQueryParams cho useTableData
  const tableDataCreateQueryParams = (
    page: number,
    pageSize: number,
    searchTerm: string,
    sortBy: string | null,
    sortDirection: SortDirection | null
  ) => {
    return createQueryParams({
      page,
      pageSize,
      searchTerm,
      sortBy,
      sortDirection,
      filterValue: filter.selectedValue,
      dateRange: dateRange.dateRange,
    });
  };

  // Sử dụng hook useTableData
  const tableData = useTableData({
    ...tableDataOptions,
    createQueryParams: tableDataCreateQueryParams,
  });

  // Sử dụng hook useColumnVisibility
  const columnVisibility = useColumnVisibility({
    columns,
    ...(columnVisibilityOptions || {}),
  });

  // Tạo query params tổng hợp
  const queryParams = useMemo(() => {
    return createQueryParams({
      page: tableData.currentPage,
      pageSize: tableData.pageSize,
      searchTerm: tableData.searchTerm,
      sortBy: tableData.sortBy,
      sortDirection: tableData.sortDirection,
      filterValue: filter.selectedValue,
      dateRange: dateRange.dateRange,
    });
  }, [
    tableData.currentPage,
    tableData.pageSize,
    tableData.searchTerm,
    tableData.sortBy,
    tableData.sortDirection,
    filter.selectedValue,
    dateRange.dateRange,
    createQueryParams,
  ]);

  // Xử lý dữ liệu từ API
  const [processedTableData, setProcessedTableData] = useState<TableData<T>>({
    items: [],
    totalItems: 0,
    currentPage: 1,
    totalPages: 0,
    loading: isLoading || false,
  });

  // Cập nhật processedTableData khi apiData thay đổi
  useEffect(() => {
    if (apiData) {
      setProcessedTableData({
        items: apiData.items,
        totalItems: apiData.meta.totalItems,
        currentPage: apiData.meta.currentPage,
        totalPages: apiData.meta.totalPages,
        loading: isLoading || false,
      });
    }
  }, [apiData, isLoading]);

  // Hàm cập nhật dữ liệu từ bên ngoài
  const updateTableData = useCallback((data: PaginatedData<T> | undefined, loading: boolean) => {
    if (data) {
      setProcessedTableData({
        items: data.items,
        totalItems: data.meta.totalItems,
        currentPage: data.meta.currentPage,
        totalPages: data.meta.totalPages,
        loading,
      });
    } else {
      setProcessedTableData(prev => ({
        ...prev,
        loading,
      }));
    }
  }, []);

  // Tạo menuItems từ filterOptions
  const createMenuItems = useCallback(
    (options: FilterOption[], selectedId: string) => {
      return options.map(option => ({
        id: option.id,
        label: option.label,
        icon: option.icon,
        onClick: () => filter.handleFilterChange(option.id),
        active: selectedId === option.id,
        keepOpen: true, // Keep menu open for filter selections
      }));
    },
    [filter]
  );

  // Tạo menuItems từ filterOptions
  const menuItems = useMemo<ModernMenuItem[]>(() => {
    if (!filterOptions || !filterOptions.options) {
      return [];
    }
    return createMenuItems(filterOptions.options, filter.selectedId);
  }, [filterOptions, filter.selectedId, createMenuItems]);

  return {
    // Từ useTableData
    tableData,

    // Từ useColumnVisibility
    columnVisibility,

    // Từ useFilterOptions
    filter,

    // Từ useDateRangeFilter
    dateRange,

    // Query params tổng hợp
    queryParams,

    // Dữ liệu đã xử lý từ API
    processedData: processedTableData,

    // MenuItems từ filterOptions
    menuItems,

    // Hàm tạo menuItems
    createMenuItems,

    // Hàm cập nhật dữ liệu từ bên ngoài
    updateTableData,
  };
}
