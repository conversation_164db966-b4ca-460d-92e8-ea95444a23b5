import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Input,
  Select,
  FormItem,
  Card,
  Toggle,
} from '@/shared/components/common';
import { BillingCycle, UsageUnit } from '../../types/plan-pricing.admin.types';

interface PlanPricingFormData {
  planId: number;
  billingCycle: BillingCycle;
  price: number;
  usageLimit: number;
  usageUnit: UsageUnit;
  isActive: boolean;
}

interface PlanPricingFormErrors {
  planId?: string;
  billingCycle?: string;
  price?: string;
  usageLimit?: string;
  usageUnit?: string;
}

interface PlanPricingFormProps {
  onSubmit: (data: PlanPricingFormData) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

/**
 * Form tạo tùy chọn giá mới
 */
const PlanPricingForm: React.FC<PlanPricingFormProps> = ({
  onSubmit,
  onCancel,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho form fields (sử dụng string cho input, convert khi submit)
  const [formData, setFormData] = useState({
    planId: '',
    billingCycle: BillingCycle.MONTH,
    price: '',
    usageLimit: '',
    usageUnit: UsageUnit.BYTES,
    isActive: true,
  });

  // State cho errors
  const [errors, setErrors] = useState<PlanPricingFormErrors>({});

  // Xử lý thay đổi planId
  const handlePlanIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFormData(prev => ({ ...prev, planId: value }));
    if (errors.planId) {
      setErrors(prev => ({ ...prev, planId: '' }));
    }
  };

  // Xử lý thay đổi billingCycle
  const handleBillingCycleChange = (value: string | number | string[] | number[]) => {
    setFormData(prev => ({ ...prev, billingCycle: value as BillingCycle }));
    if (errors.billingCycle) {
      setErrors(prev => ({ ...prev, billingCycle: '' }));
    }
  };

  // Xử lý thay đổi price
  const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFormData(prev => ({ ...prev, price: value }));
    if (errors.price) {
      setErrors(prev => ({ ...prev, price: '' }));
    }
  };

  // Xử lý thay đổi usageLimit
  const handleUsageLimitChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFormData(prev => ({ ...prev, usageLimit: value }));
    if (errors.usageLimit) {
      setErrors(prev => ({ ...prev, usageLimit: '' }));
    }
  };

  // Xử lý thay đổi usageUnit
  const handleUsageUnitChange = (value: string | number | string[] | number[]) => {
    setFormData(prev => ({ ...prev, usageUnit: value as UsageUnit }));
    if (errors.usageUnit) {
      setErrors(prev => ({ ...prev, usageUnit: '' }));
    }
  };

  // Xử lý thay đổi isActive
  const handleIsActiveChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, isActive: checked }));
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: PlanPricingFormErrors = {};

    if (!formData.planId) {
      newErrors.planId = 'ID gói dịch vụ là bắt buộc';
    } else if (isNaN(Number(formData.planId))) {
      newErrors.planId = 'ID gói dịch vụ phải là số';
    }

    if (!formData.price) {
      newErrors.price = 'Giá là bắt buộc';
    } else if (isNaN(Number(formData.price)) || Number(formData.price) < 0) {
      newErrors.price = 'Giá phải là số dương';
    }

    if (!formData.usageLimit) {
      newErrors.usageLimit = 'Giới hạn sử dụng là bắt buộc';
    } else if (isNaN(Number(formData.usageLimit)) || Number(formData.usageLimit) < 0) {
      newErrors.usageLimit = 'Giới hạn sử dụng phải là số dương';
    }

    if (!formData.billingCycle) {
      newErrors.billingCycle = 'Chu kỳ thanh toán là bắt buộc';
    }

    if (!formData.usageUnit) {
      newErrors.usageUnit = 'Đơn vị sử dụng là bắt buộc';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Xử lý submit form
  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const submitData: PlanPricingFormData = {
        planId: Number(formData.planId),
        billingCycle: formData.billingCycle,
        price: Number(formData.price),
        usageLimit: Number(formData.usageLimit),
        usageUnit: formData.usageUnit,
        isActive: formData.isActive,
      };

      await onSubmit(submitData);

      // Reset form sau khi submit thành công
      setFormData({
        planId: '',
        billingCycle: BillingCycle.MONTH,
        price: '',
        usageLimit: '',
        usageUnit: UsageUnit.BYTES,
        isActive: true,
      });
      setErrors({});
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  // Xử lý hủy
  const handleCancel = () => {
    setFormData({
      planId: '',
      billingCycle: BillingCycle.MONTH,
      price: '',
      usageLimit: '',
      usageUnit: UsageUnit.BYTES,
      isActive: true,
    });
    setErrors({});
    onCancel();
  };

  // Options cho billing cycle
  const billingCycleOptions = [
    {
      value: BillingCycle.MONTH,
      label: 'Tháng',
    },
    {
      value: BillingCycle.SIX_MONTHS,
      label: '6 Tháng',
    },
    {
      value: BillingCycle.YEAR,
      label: 'Năm',
    },
  ];

  // Options cho usage unit
  const usageUnitOptions = [
    {
      value: UsageUnit.BYTES,
      label: 'Bytes',
    },
    {
      value: UsageUnit.REQUEST,
      label: 'Request',
    },
  ];

  return (
    <Card className="w-full">
      <div className="p-6">
        <Typography variant="h3" className="mb-6">
          Tạo tùy chọn giá mới
        </Typography>

        <form onSubmit={handleFormSubmit} className="space-y-6">
          {/* ID gói dịch vụ */}
          <FormItem
            label="ID gói dịch vụ"
            required
          >
            <Input
              value={formData.planId}
              onChange={handlePlanIdChange}
              placeholder="Nhập ID gói dịch vụ"
              error={errors.planId}
              fullWidth
              type="number"
            />
          </FormItem>

          {/* Chu kỳ thanh toán */}
          <FormItem
            label="Chu kỳ thanh toán"
            required
          >
            <Select
              value={formData.billingCycle}
              onChange={handleBillingCycleChange}
              options={billingCycleOptions}
              placeholder="Chọn chu kỳ thanh toán"
              error={errors.billingCycle}
              fullWidth
            />
          </FormItem>

          {/* Giá */}
          <FormItem
            label="Giá (VND)"
            required
          >
            <Input
              value={formData.price}
              onChange={handlePriceChange}
              placeholder="Nhập giá"
              error={errors.price}
              fullWidth
              type="number"
              min="0"
              step="0.01"
            />
          </FormItem>

          {/* Giới hạn sử dụng */}
          <FormItem
            label="Giới hạn sử dụng"
            required
          >
            <Input
              value={formData.usageLimit}
              onChange={handleUsageLimitChange}
              placeholder="Nhập giới hạn sử dụng"
              error={errors.usageLimit}
              fullWidth
              type="number"
              min="0"
            />
          </FormItem>

          {/* Đơn vị sử dụng */}
          <FormItem
            label="Đơn vị sử dụng"
            required
          >
            <Select
              value={formData.usageUnit}
              onChange={handleUsageUnitChange}
              options={usageUnitOptions}
              placeholder="Chọn đơn vị sử dụng"
              error={errors.usageUnit}
              fullWidth
            />
          </FormItem>

          {/* Trạng thái kích hoạt */}
          <FormItem
            label="Trạng thái"
          >
            <div className="flex items-center space-x-2">
              <Toggle
                checked={formData.isActive}
                onChange={handleIsActiveChange}
              />
              <span className="text-sm text-gray-600">
                {formData.isActive ? 'Hoạt động' : 'Không hoạt động'}
              </span>
            </div>
          </FormItem>

          {/* Buttons */}
          <div className="flex justify-end space-x-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              {t('common:cancel')}
            </Button>
            <Button
              type="submit"
              variant="primary"
              isLoading={isSubmitting}
              disabled={isSubmitting}
            >
              Tạo tùy chọn giá
            </Button>
          </div>
        </form>
      </div>
    </Card>
  );
};

export default PlanPricingForm;
