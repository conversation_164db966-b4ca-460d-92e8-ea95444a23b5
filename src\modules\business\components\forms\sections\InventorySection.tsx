import React from 'react';
import {
  CollapsibleCard,
  Typography,
  FormItem,
  Input,
  IconCard,
} from '@/shared/components/common';
import { AsyncSelectWithPagination } from '@/shared/components/common/AsyncSelectWithPagination';
import { WarehouseService } from '@/modules/business/services/warehouse.service';
import { InventorySectionProps } from './product-form-types';

const InventorySection: React.FC<InventorySectionProps> = ({
  t,
  productInventories,
  handleAddInventory,
  handleRemoveInventory,
  handleUpdateInventory,
}) => {
  return (
    <CollapsibleCard
      title={
        <div className="flex items-center justify-between w-full">
          <Typography variant="h6" className="font-medium">
            {productInventories.length > 0
              ? `${t('business:product.form.sections.inventory', '5. Quản lý tồn kho')} (${productInventories.length})`
              : t('business:product.form.sections.inventory', '5. Quản lý tồn kho')
            }
          </Typography>
          <div
            onClick={e => {
              e.preventDefault();
              e.stopPropagation(); // Ngăn không cho toggle card
            }}
          >
            <IconCard
              icon="plus"
              variant="primary"
              size="sm"
              title={t('business:product.form.inventory.addInventory', 'Thêm kho')}
              onClick={handleAddInventory}
            />
          </div>
        </div>
      }
      defaultOpen={false}
      className="mb-4"
    >
      {/* Danh sách kho */}
      {productInventories.length > 0 ? (
        <div className="space-y-4">
          {productInventories.map((inventory, index) => (
            <CollapsibleCard
              key={inventory.id}
              title={
                <div className="flex justify-between items-center w-full">
                  <div className="flex items-center space-x-4">
                    <Typography variant="body2" className="font-medium">
                      {t('business:product.form.inventory.warehouse', 'Kho')} {index + 1}
                    </Typography>
                    {inventory.availableQuantity && (
                      <Typography variant="body2" className="text-gray-500 dark:text-gray-500">
                        {inventory.availableQuantity} sản phẩm
                      </Typography>
                    )}
                    {inventory.sku && (
                      <Typography variant="body2" className="text-gray-400 dark:text-gray-600 text-xs">
                        SKU: {inventory.sku}
                      </Typography>
                    )}
                  </div>
                  <div
                    onClick={e => {
                      e.stopPropagation();
                      handleRemoveInventory(inventory.id);
                    }}
                    className="cursor-pointer"
                  >
                    <IconCard
                      icon="trash"
                      variant="danger"
                      size="sm"
                      title={t('common:delete', 'Xóa')}
                    />
                  </div>
                </div>
              }
              defaultOpen={true}
            >
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItem
                    label={t('business:product.form.inventory.warehouse', 'Kho')}
                    required
                  >
                    <AsyncSelectWithPagination
                      fullWidth
                      value={inventory.warehouseId || ''}
                      onChange={(value) => {
                        const warehouseId = Array.isArray(value) ? value[0] : value;
                        handleUpdateInventory(inventory.id, 'warehouseId', warehouseId || '');
                      }}
                      placeholder={t(
                        'business:product.form.inventory.warehousePlaceholder',
                        'Chọn kho'
                      )}
                      loadOptions={async ({
                        search,
                        page,
                        limit,
                      }: {
                        search?: string;
                        page?: number;
                        limit?: number;
                      }) => {
                        // Gọi API thực tế để lấy danh sách kho
                        const params: { page: number; limit: number; search?: string } = {
                          page: page || 1,
                          limit: limit || 20,
                        };

                        // Chỉ thêm search nếu có giá trị
                        if (search) {
                          params.search = search;
                        }

                        const response = await WarehouseService.getWarehousesForSelect(params);

                        return response;
                      }}
                      searchOnEnter={true}
                    />
                  </FormItem>

                  <FormItem
                    label={t('business:product.form.inventory.availableQuantity', 'Số lượng có sẵn')}
                  >
                    <Input 
                      fullWidth 
                      type="number" 
                      min="0" 
                      placeholder="0"
                      value={inventory.availableQuantity}
                      onChange={(e) => handleUpdateInventory(inventory.id, 'availableQuantity', e.target.value)}
                    />
                  </FormItem>

                  <FormItem
                    label={t('business:product.form.inventory.sku', 'SKU')}
                  >
                    <Input 
                      fullWidth 
                      placeholder="SKU-001"
                      value={inventory.sku}
                      onChange={(e) => handleUpdateInventory(inventory.id, 'sku', e.target.value)}
                    />
                  </FormItem>

                  <FormItem
                    label={t('business:product.form.inventory.barcode', 'Barcode')}
                  >
                    <Input 
                      fullWidth 
                      placeholder="1234567890123"
                      value={inventory.barcode}
                      onChange={(e) => handleUpdateInventory(inventory.id, 'barcode', e.target.value)}
                    />
                  </FormItem>
                </div>
              </div>
            </CollapsibleCard>
          ))}
        </div>
      ) : (
        <div className="text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
            {t(
              'business:product.form.inventory.noInventories',
              'Chưa có kho nào. Nhấn "Thêm kho" để bắt đầu.'
            )}
          </Typography>
        </div>
      )}
    </CollapsibleCard>
  );
};

export default InventorySection;
