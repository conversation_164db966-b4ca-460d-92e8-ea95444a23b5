import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { NotificationUtil } from '@/shared/utils/notification';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { ShopService } from '../services/shop.service';
import { CreateOrUpdateShopInfoDto } from '../types/shop.types';

/**
 * Query keys cho shop
 */
export const SHOP_QUERY_KEYS = {
  all: ['shop'] as const,
  list: (params?: ShopQueryParams) => [...SHOP_QUERY_KEYS.all, 'list', params] as const,
  info: () => [...SHOP_QUERY_KEYS.all, 'info'] as const,
  detail: (id: string) => [...SHOP_QUERY_KEYS.all, 'detail', id] as const,
  byId: (id: string) => [...SHOP_QUERY_KEYS.all, 'byId', id] as const,
};

/**
 * Interface cho shop query params
 */
export interface ShopQueryParams {
  page?: number;
  limit?: number;
  search?: string | undefined;
  sortBy?: string | undefined;
  sortDirection?: SortDirection | undefined;
}

/**
 * Hook để lấy danh sách cửa hàng
 */
export const useShopList = (params?: ShopQueryParams) => {
  return useQuery({
    queryKey: SHOP_QUERY_KEYS.list(params),
    queryFn: async () => {
      try {
        const response = await ShopService.getShopList(params);
        console.log('🔍 [useShopList] API response:', response);

        // Đảm bảo luôn trả về array
        const data = response.result.items || [];
        if (Array.isArray(data)) {
          return data;
        }

        // Nếu data không phải array, log warning và trả về empty array
        console.warn('🔍 [useShopList] API response is not an array:', data);
        return [];
      } catch (error) {
        console.error('🔍 [useShopList] Error:', error);
        throw error;
      }
    },
    retry: (failureCount, error: Error & { response?: { status: number } }) => {
      // Nếu lỗi 404 (chưa có cửa hàng), không retry
      if (error?.response?.status === 404) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

/**
 * Hook để lấy thông tin cửa hàng đơn lẻ (giữ lại để tương thích)
 */
export const useShopInfo = () => {
  return useQuery({
    queryKey: SHOP_QUERY_KEYS.info(),
    queryFn: async () => {
      try {
        const response = await ShopService.getShopInfo();
        console.log('🔍 [useShopInfo] API response:', response);
        return response.result || response.result;
      } catch (error) {
        console.error('🔍 [useShopInfo] Error:', error);
        throw error;
      }
    },
    retry: (failureCount, error: Error & { response?: { status: number } }) => {
      // Nếu lỗi 404 (chưa có thông tin cửa hàng), không retry
      if (error?.response?.status === 404) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

/**
 * Hook để lấy thông tin chi tiết cửa hàng theo ID
 */
export const useShopDetail = (id: string) => {
  return useQuery({
    queryKey: SHOP_QUERY_KEYS.detail(id),
    queryFn: async () => {
      try {
        const response = await ShopService.getShopDetail(id);
        console.log('🔍 [useShopDetail] API response:', response);
        return response.result || response.result;
      } catch (error) {
        console.error('🔍 [useShopDetail] Error:', error);
        throw error;
      }
    },
    enabled: !!id, // Chỉ chạy query khi có ID
    retry: (failureCount, error: Error & { response?: { status: number } }) => {
      // Nếu lỗi 404 (không tìm thấy cửa hàng), không retry
      if (error?.response?.status === 404) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

/**
 * Hook để tạo thông tin cửa hàng
 */
export const useCreateShopInfo = () => {
  const { t } = useTranslation('business');
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateOrUpdateShopInfoDto) => ShopService.createShopInfo(data),
    onSuccess: () => {
      NotificationUtil.success({
        message: t('business:shop.messages.createSuccess'),
      });
      // Invalidate tất cả queries liên quan đến shop list
      console.log('🔄 [useCreateShopInfo] Invalidating shop queries...');
      queryClient.invalidateQueries({ queryKey: SHOP_QUERY_KEYS.all });
    },
    onError: (error: Error) => {
      console.error('Create shop info error:', error);
      NotificationUtil.error({
        message: t('business:shop.messages.createError'),
      });
    },
  });
};

/**
 * Hook để cập nhật thông tin cửa hàng
 */
export const useUpdateShopInfo = () => {
  const { t } = useTranslation('business');
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: CreateOrUpdateShopInfoDto }) =>
      ShopService.updateShopInfo(id, data),
    onSuccess: () => {
      NotificationUtil.success({
        message: t('business:shop.messages.updateSuccess'),
      });
      // Invalidate tất cả queries liên quan đến shop
      console.log('🔄 [useUpdateShopInfo] Invalidating shop queries...');
      queryClient.invalidateQueries({ queryKey: SHOP_QUERY_KEYS.all });
    },
    onError: (error: Error) => {
      console.error('Update shop info error:', error);
      NotificationUtil.error({
        message: t('business:shop.messages.updateError'),
      });
    },
  });
};

/**
 * Hook để xóa nhiều cửa hàng
 */
export const useDeleteMultipleShops = () => {
  const { t } = useTranslation('business');
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (shopIds: number[]) => ShopService.deleteMultipleShops(shopIds),
    onSuccess: () => {
      // Invalidate tất cả queries liên quan đến shop
      queryClient.invalidateQueries({ queryKey: SHOP_QUERY_KEYS.all });
    },
    onError: (error: Error) => {
      console.error('Delete multiple shops error:', error);
      NotificationUtil.error({
        message: t('business:shop.messages.bulkDeleteError'),
      });
    },
  });
};

/**
 * Hook để lấy thông tin shop theo ID
 */
export const useShopById = (id: string) => {
  return useQuery({
    queryKey: SHOP_QUERY_KEYS.byId(id),
    queryFn: async () => {
      try {
        const response = await ShopService.getShopById(id);
        return response.result;
      } catch (error) {
        console.error('🔍 [useShopById] Error:', error);
        throw error;
      }
    },
    enabled: !!id, // Chỉ chạy query khi có ID
    retry: (failureCount, error: Error & { response?: { status: number } }) => {
      // Nếu lỗi 404 (không tìm thấy shop), không retry
      if (error?.response?.status === 404) {
        return false;
      }
      return failureCount < 3;
    },
  });
};
