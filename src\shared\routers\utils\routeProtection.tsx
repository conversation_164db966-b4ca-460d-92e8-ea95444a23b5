import React, { useEffect } from 'react';
import { RouteObject, Navigate, useLocation } from 'react-router-dom';
import { useAuthCommon, AuthType } from '@/shared/hooks/useAuthCommon';

/**
 * Component bảo vệ route - kiểm tra token và điều hướng nếu cần
 */
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredAuthType?: AuthType;
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredAuthType = AuthType.USER,
  redirectTo
}) => {
  const { isTokenValid, authType, isAuthenticated, clearAuth } = useAuthCommon();
  const location = useLocation();

  useEffect(() => {
    // Log thông tin debug
    console.log('ProtectedRoute check:', {
      currentPath: location.pathname,
      requiredAuthType,
      currentAuthType: authType,
      isAuthenticated,
      isTokenValid: isTokenValid(),
      redirectTo
    });
  }, [location.pathname, requiredAuthType, authType, isAuthenticated, isTokenValid, redirectTo]);

  // Kiểm tra token có hợp lệ không
  const tokenValid = isTokenValid();

  if (!tokenValid || !isAuthenticated) {
    // Xóa auth data nếu token không hợp lệ
    if (!tokenValid) {
      clearAuth();
    }

    // Điều hướng dựa trên loại auth yêu cầu
    const defaultRedirect = requiredAuthType === AuthType.ADMIN ? '/admin/auth' : '/auth';
    console.log('Token invalid or not authenticated, redirecting to:', redirectTo || defaultRedirect);
    return <Navigate to={redirectTo || defaultRedirect} replace />;
  }

  // Kiểm tra loại auth có đúng không
  if (requiredAuthType !== AuthType.NONE && authType !== requiredAuthType) {
    const defaultRedirect = requiredAuthType === AuthType.ADMIN ? '/admin/auth' : '/auth';
    console.log('Auth type mismatch, redirecting to:', redirectTo || defaultRedirect);
    return <Navigate to={redirectTo || defaultRedirect} replace />;
  }

  return <>{children}</>;
};

/**
 * Hàm wrap routes với protection
 */
export const wrapRoutesWithAuth = (
  routes: RouteObject[], 
  requiredAuthType: AuthType = AuthType.USER,
  redirectTo?: string
): RouteObject[] => {
  return routes.map(route => ({
    ...route,
    element: route.element ? (
      <ProtectedRoute requiredAuthType={requiredAuthType} redirectTo={redirectTo}>
        {route.element}
      </ProtectedRoute>
    ) : route.element,
    children: route.children ? wrapRoutesWithAuth(route.children, requiredAuthType, redirectTo) : route.children,
  }));
};

/**
 * Hàm kiểm tra xem route có phải là auth route không
 */
const isAuthRoute = (path: string): boolean => {
  return path.startsWith('/auth') || path.startsWith('/admin/auth');
};

/**
 * Hàm kiểm tra xem route có phải là public route không (không cần authentication)
 */
const isPublicRoute = (path: string): boolean => {
  const publicPaths = [
    '/auth',
    '/admin/auth',
    // Có thể thêm các route public khác ở đây nếu cần
  ];

  return publicPaths.some(publicPath => path.startsWith(publicPath));
};

/**
 * Hàm phân loại routes thành user routes và admin routes
 */
export const categorizeRoutes = (routes: RouteObject[]) => {
  const userRoutes: RouteObject[] = [];
  const adminRoutes: RouteObject[] = [];
  const publicRoutes: RouteObject[] = [];

  routes.forEach(route => {
    if (!route.path) {
      // Routes không có path (như error routes) được coi là public
      publicRoutes.push(route);
      return;
    }

    if (isPublicRoute(route.path)) {
      publicRoutes.push(route);
    } else if (route.path.startsWith('/admin')) {
      adminRoutes.push(route);
    } else {
      userRoutes.push(route);
    }
  });

  console.log('Route categorization:', {
    publicRoutes: publicRoutes.length,
    userRoutes: userRoutes.length,
    adminRoutes: adminRoutes.length,
    publicPaths: publicRoutes.map(r => r.path).filter(Boolean),
    userPaths: userRoutes.map(r => r.path).filter(Boolean).slice(0, 5), // Show first 5
    adminPaths: adminRoutes.map(r => r.path).filter(Boolean).slice(0, 5), // Show first 5
  });

  return { userRoutes, adminRoutes, publicRoutes };
};

export default ProtectedRoute;
