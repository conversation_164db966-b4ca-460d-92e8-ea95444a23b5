import React, { useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, ActionMenuItem, Chip } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import {
  useGetPlanPricings,
  useCreatePlanPricing,
  useUpdatePlanPricing,
  useTogglePlanPricingStatus,
} from '../hooks/usePlanPricingAdmin';
import {
  PlanPricing,
  GetPlanPricingsQueryDto,
  CreatePlanPricingDto,
  UpdatePlanPricingDto,
  BillingCycle,
  UsageUnit,
} from '../types/plan-pricing.admin.types';

interface PlanPricingFormData {
  planId: number;
  billingCycle: BillingCycle;
  price: number;
  usageLimit: number;
  usageUnit: UsageUnit;
  isActive: boolean;
}
import PlanPricingForm from '../components/forms/PlanPricingForm';
import PlanPricingEditForm from '../components/forms/PlanPricingEditForm';
import PlanPricingDetailForm from '../components/forms/PlanPricingDetailForm';
import DeletePlanPricingModal from '../components/modals/DeletePlanPricingModal';

/**
 * Trang quản lý tùy chọn giá gói dịch vụ
 */
const PlanPricingListPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho form và modal
  const [selectedPricing, setSelectedPricing] = useState<PlanPricing | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form chỉnh sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form chi tiết
  const {
    isVisible: isDetailFormVisible,
    showForm: showDetailForm,
    hideForm: hideDetailForm,
  } = useSlideForm();

  // Mutations
  const { mutateAsync: createPricing, isPending: isCreating } = useCreatePlanPricing();
  const { mutateAsync: updatePricing, isPending: isUpdating } = useUpdatePlanPricing();
  const { mutateAsync: toggleStatus } = useTogglePlanPricingStatus();

  // Xử lý xem chi tiết
  const handleViewDetail = useCallback(
    (pricing: PlanPricing) => {
      setSelectedPricing(pricing);
      showDetailForm();
    },
    [showDetailForm]
  );

  // Xử lý chỉnh sửa
  const handleEdit = useCallback(
    (pricing: PlanPricing) => {
      setSelectedPricing(pricing);
      showEditForm();
    },
    [showEditForm]
  );

  // Xử lý xóa
  const handleDelete = useCallback((pricing: PlanPricing) => {
    setSelectedPricing(pricing);
    setIsDeleteModalOpen(true);
  }, []);

  // Xử lý toggle trạng thái
  const handleToggleStatus = useCallback(
    async (pricing: PlanPricing) => {
      try {
        await toggleStatus({ id: pricing.id, isActive: !pricing.isActive });
      } catch (error) {
        console.error('Lỗi khi thay đổi trạng thái:', error);
      }
    },
    [toggleStatus]
  );

  // Hàm render billing cycle chip
  const renderBillingCycle = useCallback((billingCycle: BillingCycle) => {
    const cycleConfig = {
      [BillingCycle.MONTH]: {
        label: t('admin:subscription.pricing.billingCycle.month'),
        variant: 'primary' as const,
      },
      [BillingCycle.SIX_MONTHS]: {
        label: t('admin:subscription.pricing.billingCycle.sixMonths'),
        variant: 'info' as const,
      },
      [BillingCycle.YEAR]: {
        label: t('admin:subscription.pricing.billingCycle.year'),
        variant: 'success' as const,
      },
    };

    const config = cycleConfig[billingCycle] || {
      label: billingCycle || 'Unknown',
      variant: 'default' as const,
    };

    return (
      <Chip variant={config.variant} size="sm">
        {config.label}
      </Chip>
    );
  }, [t]);

  // Hàm render usage unit
  const renderUsageUnit = useCallback((usageUnit: UsageUnit) => {
    const unitConfig = {
      [UsageUnit.BYTES]: {
        label: t('admin:subscription.pricing.usageUnit.bytes'),
        variant: 'info' as const,
      },
      [UsageUnit.REQUEST]: {
        label: t('admin:subscription.pricing.usageUnit.request'),
        variant: 'primary' as const,
      },
    };

    const config = unitConfig[usageUnit] || {
      label: usageUnit || 'Unknown',
      variant: 'default' as const,
    };

    return (
      <Chip variant={config.variant} size="sm">
        {config.label}
      </Chip>
    );
  }, [t]);

  // Hàm render trạng thái
  const renderStatus = useCallback((isActive: boolean) => {
    // Đảm bảo isActive là boolean
    const status = Boolean(isActive);

    return (
      <Chip
        variant={status ? 'success' : 'danger'}
        size="sm"
      >
        {status ? t('admin:subscription.pricing.status.active') : t('admin:subscription.pricing.status.inactive')}
      </Chip>
    );
  }, [t]);

  // Hàm format giá tiền
  const formatPrice = useCallback((price: string) => {
    if (!price) return '0 ₫';

    const numPrice = parseFloat(price);
    if (isNaN(numPrice)) return '0 ₫';

    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(numPrice);
  }, []);

  // Hàm format usage limit
  const formatUsageLimit = useCallback((usageLimit: string, usageUnit: UsageUnit) => {
    if (!usageLimit) return '0';

    const numLimit = parseFloat(usageLimit);
    if (isNaN(numLimit)) return '0';

    if (usageUnit === UsageUnit.BYTES) {
      // Convert bytes to GB
      const gb = numLimit / (1024 * 1024 * 1024);
      return `${gb.toFixed(2)} GB`;
    }
    return `${numLimit.toLocaleString('vi-VN')} ${usageUnit}`;
  }, []);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<PlanPricing>[]>(
    () => [
      {
        key: 'id',
        title: 'ID',
        dataIndex: 'id',
        width: '6%',
        sortable: true,
      },
      {
        key: 'planId',
        title: 'Plan ID',
        dataIndex: 'planId',
        width: '8%',
        sortable: true,
      },
      {
        key: 'billingCycle',
        title: t('admin:subscription.pricing.table.billingCycle'),
        dataIndex: 'billingCycle',
        width: '12%',
        sortable: true,
        render: (value: unknown) => {
          return renderBillingCycle(value as BillingCycle);
        },
      },
      {
        key: 'price',
        title: t('admin:subscription.pricing.table.price'),
        dataIndex: 'price',
        width: '12%',
        sortable: true,
        render: (value: unknown) => {
          return (
            <div className="font-medium text-green-600">
              {formatPrice(value as string)}
            </div>
          );
        },
      },
      {
        key: 'usageLimit',
        title: t('admin:subscription.pricing.table.usageLimit'),
        dataIndex: 'usageLimit',
        width: '15%',
        render: (value: unknown, record: PlanPricing) => {
          return (
            <div className="text-sm">
              {formatUsageLimit(value as string, record.usageUnit)}
            </div>
          );
        },
      },
      {
        key: 'usageUnit',
        title: t('admin:subscription.pricing.table.usageUnit'),
        dataIndex: 'usageUnit',
        width: '10%',
        render: (value: unknown) => {
          return renderUsageUnit(value as UsageUnit);
        },
      },
      {
        key: 'isActive',
        title: t('admin:subscription.pricing.table.status'),
        dataIndex: 'isActive',
        width: '12%',
        sortable: true,
        render: (value: unknown) => {
          return renderStatus(value as boolean);
        },
      },
      {
        key: 'createdAt',
        title: t('admin:subscription.pricing.table.createdAt'),
        dataIndex: 'createdAt',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const timestamp = value as string;
          if (!timestamp) return '-';

          const date = new Date(parseInt(timestamp));
          if (isNaN(date.getTime())) return '-';

          return (
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {date.toLocaleDateString('vi-VN')}
            </div>
          );
        },
      },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '15%',
        render: (_: unknown, record: PlanPricing) => {
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view-detail',
              label: t('admin:subscription.pricing.actions.viewDetail'),
              icon: 'eye',
              onClick: () => handleViewDetail(record),
            },
            {
              id: 'edit',
              label: t('common:edit'),
              icon: 'edit',
              onClick: () => handleEdit(record),
            },
            {
              id: 'toggle-status',
              label: record.isActive ? t('admin:subscription.pricing.actions.deactivate') : t('admin:subscription.pricing.actions.activate'),
              icon: record.isActive ? 'eye-off' : 'check',
              onClick: () => handleToggleStatus(record),
            },
            {
              id: 'delete',
              label: t('common:delete'),
              icon: 'trash',
              onClick: () => handleDelete(record),
            },
          ];

          return (
            <div className="flex justify-center">
              <ActionMenu
                items={actionItems}
                menuTooltip={t('common:moreActions', 'Thêm hành động')}
                iconSize="sm"
                iconVariant="default"
                placement="bottom"
                menuWidth="180px"
                menuIcon="more-horizontal"
                showAllInMenu={true}
                preferRight={true}
                preferTop={true}
              />
            </div>
          );
        },
      },
    ],
    [t, handleViewDetail, handleEdit, handleDelete, handleToggleStatus, renderBillingCycle, renderUsageUnit, renderStatus, formatPrice, formatUsageLimit]
  );

  // Filter options
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      {
        id: 'active',
        label: t('admin:subscription.pricing.status.active'),
        icon: 'check-circle',
        value: true,
      },
      {
        id: 'inactive',
        label: t('admin:subscription.pricing.status.inactive'),
        icon: 'x-circle',
        value: false,
      },
      {
        id: 'month',
        label: t('admin:subscription.pricing.billingCycle.month'),
        icon: 'calendar',
        value: BillingCycle.MONTH,
      },
      {
        id: 'year',
        label: t('admin:subscription.pricing.billingCycle.year'),
        icon: 'calendar',
        value: BillingCycle.YEAR,
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): GetPlanPricingsQueryDto => {
    const queryParams: GetPlanPricingsQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue !== 'all') {
      if (typeof params.filterValue === 'boolean') {
        queryParams.isActive = params.filterValue;
      } else if (Object.values(BillingCycle).includes(params.filterValue as BillingCycle)) {
        queryParams.billingCycle = params.filterValue as BillingCycle;
      }
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<PlanPricing, GetPlanPricingsQueryDto>({
      columns,
      filterOptions,
      showDateFilter: true,
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách plan pricing với queryParams từ dataTable
  const { data: pricingData, isLoading } = useGetPlanPricings(dataTable.queryParams);

  // Xử lý thêm mới
  const handleAdd = () => {
    showAddForm();
  };

  // Xử lý submit form thêm mới
  const handleSubmit = async (values: PlanPricingFormData) => {
    try {
      const createPricingDto: CreatePlanPricingDto = {
        planId: values.planId,
        billingCycle: values.billingCycle,
        price: values.price,
        usageLimit: values.usageLimit,
        usageUnit: values.usageUnit,
        isActive: values.isActive,
      };

      await createPricing(createPricingDto);
      hideAddForm();
    } catch (error) {
      console.error(t('admin:subscription.pricing.actions.createError'), error);
    }
  };

  // Xử lý submit form chỉnh sửa
  const handleUpdateSubmit = async (id: number, data: UpdatePlanPricingDto) => {
    try {
      await updatePricing({ id, data });
      hideEditForm();
      setTimeout(() => {
        setSelectedPricing(null);
      }, 300);
    } catch (error) {
      console.error(t('admin:subscription.pricing.actions.updateError'), error);
    }
  };

  // Xử lý hủy form thêm mới
  const handleCancel = () => {
    hideAddForm();
  };

  // Xử lý hủy form chỉnh sửa
  const handleEditCancel = () => {
    hideEditForm();
    setTimeout(() => {
      setSelectedPricing(null);
    }, 300);
  };

  // Xử lý đóng form chi tiết
  const handleDetailClose = () => {
    hideDetailForm();
    setTimeout(() => {
      setSelectedPricing(null);
    }, 300);
  };

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [BillingCycle.MONTH]: t('admin:subscription.pricing.billingCycle.month'),
      [BillingCycle.YEAR]: t('admin:subscription.pricing.billingCycle.year'),
      [BillingCycle.SIX_MONTHS]: t('admin:subscription.pricing.billingCycle.sixMonths'),
      true: t('admin:subscription.pricing.status.active'),
      false: t('admin:subscription.pricing.status.inactive'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
      />

      {/* Component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form thêm mới */}
      <SlideInForm isVisible={isAddFormVisible}>
        <PlanPricingForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={isCreating}
        />
      </SlideInForm>

      {/* Form chỉnh sửa */}
      <SlideInForm isVisible={isEditFormVisible}>
        {selectedPricing && (
          <PlanPricingEditForm
            pricing={selectedPricing}
            onSubmit={handleUpdateSubmit}
            onCancel={handleEditCancel}
            isSubmitting={isUpdating}
          />
        )}
      </SlideInForm>

      {/* Form chi tiết */}
      <SlideInForm isVisible={isDetailFormVisible}>
        {selectedPricing && (
          <PlanPricingDetailForm
            pricingId={selectedPricing.id}
            onClose={handleDetailClose}
          />
        )}
      </SlideInForm>

      {/* Modal xác nhận xóa */}
      {selectedPricing && (
        <DeletePlanPricingModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          pricing={selectedPricing}
          onSuccess={() => {
            setSelectedPricing(null);
            setIsDeleteModalOpen(false);
          }}
        />
      )}

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={pricingData?.result?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: pricingData?.result?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: pricingData?.result?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default PlanPricingListPage;
