import React, { useState, useEffect, useCallback } from 'react';
import {
  Input,
  Textarea,
  Select,
  Checkbox,
  Typography,
  DatePicker,
} from '@/shared/components/common';
import Toggle from '@/shared/components/common/Toggle';
import { useTranslation } from 'react-i18next';

interface CustomFieldRendererProps {
  field: {
    id: number;
    fieldId: number;
    label: string;
    component: string;
    type: string;
    required: boolean;
    configJson: Record<string, unknown>;
    value: Record<string, unknown>;
  };
  value: string | number | boolean;
  onChange: (value: string | number | boolean) => void;
  onRemove: () => void;
}

/**
 * Component để render custom field dựa trên type
 */
const CustomFieldRenderer: React.FC<CustomFieldRendererProps> = ({
  field,
  value,
  onChange,
  onRemove,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const [error, setError] = useState<string>('');

  // Lấy config từ configJson
  const config = field.configJson || {};
  const placeholder = (config as Record<string, unknown>)['placeholder'] as string || t('business:product.form.customFields.valuePlaceholder', 'Nhập giá trị');

  // Xử lý options - hỗ trợ cả label và title
  // Kiểm tra options từ config.options hoặc config.validation.options
  const configOptions = (config as Record<string, unknown>)['options'] as Record<string, unknown>[] || [];
  const validationOptions = ((config as Record<string, unknown>)['validation'] as Record<string, unknown>)?.['options'] as Record<string, unknown>[] || [];
  const rawOptions = configOptions.length > 0 ? configOptions : validationOptions;

  const options = rawOptions.map(opt => ({
    label: (opt['label'] as string) || (opt['title'] as string) || String(opt['value']),
    value: opt['value']
  }));

  // Lấy validation config
  const validation = (config as Record<string, unknown>)['validation'] as Record<string, unknown> || {};
  const minLength = validation['minLength'] as number;
  const maxLength = validation['maxLength'] as number;
  const pattern = validation['pattern'] as string;
  const min = validation['min'] as number;
  const max = validation['max'] as number;

  // Validation function
  const validateValue = useCallback((val: string | number | boolean): string => {
    const stringValue = String(val || '');

    // Required validation
    if (field.required && (!val || stringValue.trim() === '')) {
      return t('business:product.form.customFields.validation.required', 'Trường này là bắt buộc');
    }

    // Skip other validations if value is empty and not required
    if (!val || stringValue.trim() === '') {
      return '';
    }

    const componentType = (field.component || field.type || 'text').toLowerCase();

    // Text validations
    if (componentType === 'text' || componentType === 'textarea' || componentType === 'email' || componentType === 'url') {
      if (minLength && stringValue.length < minLength) {
        return t('business:product.form.customFields.validation.minLength', 'Tối thiểu {{min}} ký tự', { min: minLength });
      }
      if (maxLength && stringValue.length > maxLength) {
        return t('business:product.form.customFields.validation.maxLength', 'Tối đa {{max}} ký tự', { max: maxLength });
      }
      if (pattern) {
        const regex = new RegExp(pattern);
        if (!regex.test(stringValue)) {
          return t('business:product.form.customFields.validation.pattern', 'Định dạng không hợp lệ');
        }
      }
    }

    // Number validations
    if (componentType === 'number') {
      const numValue = Number(val);
      if (isNaN(numValue)) {
        return t('business:product.form.customFields.validation.invalidNumber', 'Phải là số hợp lệ');
      }
      if (min !== undefined && numValue < min) {
        return t('business:product.form.customFields.validation.min', 'Tối thiểu {{min}}', { min });
      }
      if (max !== undefined && numValue > max) {
        return t('business:product.form.customFields.validation.max', 'Tối đa {{max}}', { max });
      }
    }

    // Email validation
    if (componentType === 'email') {
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (!emailRegex.test(stringValue)) {
        return t('business:product.form.customFields.validation.invalidEmail', 'Email không hợp lệ');
      }
    }

    // URL validation
    if (componentType === 'url') {
      try {
        new URL(stringValue);
      } catch {
        return t('business:product.form.customFields.validation.invalidUrl', 'URL không hợp lệ');
      }
    }

    // Date validation
    if (componentType === 'date') {
      if (stringValue && isNaN(Date.parse(stringValue))) {
        return t('business:product.form.customFields.validation.invalidDate', 'Ngày không hợp lệ');
      }
    }

    return '';
  }, [field.required, field.component, field.type, minLength, maxLength, pattern, min, max, t]);

  // Validate on value change - only when value actually changes
  useEffect(() => {
    const errorMessage = validateValue(value);
    setError(errorMessage);
  }, [value, validateValue]);

  // Handle value change with validation
  const handleChange = (newValue: string | number | boolean) => {
    onChange(newValue);
    const errorMessage = validateValue(newValue);
    setError(errorMessage);
  };

  // Render component dựa trên type
  const renderInput = () => {
    const componentType = (field.component || field.type || 'text').toLowerCase();
    switch (componentType) {
      case 'textarea':
        return (
          <div>
            <Textarea
              fullWidth
              value={value as string || ''}
              onChange={(e) => handleChange(e.target.value)}
              placeholder={placeholder}
              rows={3}
            />
            {error && (
              <Typography variant="caption" className="text-red-500 mt-1">
                {error}
              </Typography>
            )}
          </div>
        );

      case 'number':
        return (
          <div>
            <Input
              fullWidth
              type="number"
              value={typeof value === 'number' ? value.toString() : (value as string || '')}
              onChange={(e) => {
                const inputValue = e.target.value;
                if (inputValue === '') {
                  handleChange(0); // Default to 0 for empty input
                } else {
                  const numValue = Number(inputValue);
                  if (!isNaN(numValue)) {
                    handleChange(numValue);
                  }
                }
              }}
              placeholder={placeholder}
              min={min}
              max={max}
            />
            {error && (
              <Typography variant="caption" className="text-red-500 mt-1">
                {error}
              </Typography>
            )}
          </div>
        );

      case 'email':
        return (
          <div>
            <Input
              fullWidth
              type="email"
              value={value as string || ''}
              onChange={(e) => handleChange(e.target.value)}
              placeholder={placeholder}
            />
            {error && (
              <Typography variant="caption" className="text-red-500 mt-1">
                {error}
              </Typography>
            )}
          </div>
        );

      case 'phone':
        return (
          <div>
            <Input
              fullWidth
              type="tel"
              value={value as string || ''}
              onChange={(e) => handleChange(e.target.value)}
              placeholder={placeholder}
            />
            {error && (
              <Typography variant="caption" className="text-red-500 mt-1">
                {error}
              </Typography>
            )}
          </div>
        );

      case 'url':
        return (
          <div>
            <Input
              fullWidth
              type="url"
              value={value as string || ''}
              onChange={(e) => handleChange(e.target.value)}
              placeholder={placeholder}
            />
            {error && (
              <Typography variant="caption" className="text-red-500 mt-1">
                {error}
              </Typography>
            )}
          </div>
        );

      case 'password':
        return (
          <Input
            fullWidth
            type="password"
            value={value as string || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
          />
        );

      case 'date':
        return (
          <div>
            <DatePicker
              fullWidth
              noBorder={true}
              value={value ? (() => {
                try {
                  const dateStr = value as string;
                  if (!dateStr) return null;

                  // Parse YYYY-MM-DD format để tránh vấn đề timezone
                  const [year, month, day] = dateStr.split('-').map(Number);
                  if (year && month && day) {
                    return new Date(year, month - 1, day); // month - 1 vì Date constructor dùng 0-based month
                  }

                  return new Date(dateStr);
                } catch {
                  return null;
                }
              })() : null}
              onChange={(date) => {
                if (!date) {
                  handleChange('');
                  return;
                }
                // Sử dụng local date để tránh vấn đề timezone
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const dateValue = `${year}-${month}-${day}`;
                handleChange(dateValue);
              }}
              placeholder={placeholder}
              format="dd/MM/yyyy"
            />
            {error && (
              <Typography variant="caption" className="text-red-500 mt-1">
                {error}
              </Typography>
            )}
          </div>
        );

      case 'time':
        return (
          <Input
            fullWidth
            type="time"
            value={value as string || ''}
            onChange={(e) => onChange(e.target.value)}
          />
        );

      case 'datetime':
        return (
          <Input
            fullWidth
            type="datetime-local"
            value={value as string || ''}
            onChange={(e) => onChange(e.target.value)}
          />
        );

      case 'color':
        return (
          <Input
            fullWidth
            type="color"
            value={value as string || '#000000'}
            onChange={(e) => onChange(e.target.value)}
          />
        );

      case 'range':
        return (
          <Input
            fullWidth
            type="range"
            value={value as string || '0'}
            onChange={(e) => onChange(e.target.value)}
            min={(config as Record<string, unknown>)['min'] as number || 0}
            max={(config as Record<string, unknown>)['max'] as number || 100}
          />
        );

      case 'select': {
        console.log('🔍 CustomFieldRenderer - Select field debug:', {
          fieldLabel: field.label,
          fieldType: field.type,
          fieldComponent: field.component,
          configJson: field.configJson,
          configJsonType: typeof field.configJson,
          configJsonStringified: JSON.stringify(field.configJson, null, 2)
        });

        // Lấy options trực tiếp từ configJson.options
        let selectOptions: Array<{ label: string; value: string }> = [];

        // Kiểm tra nếu configJson có options
        if (field.configJson && typeof field.configJson === 'object') {
          const configJsonTyped = field.configJson as Record<string, unknown>;

          console.log('🔍 ConfigJson analysis:', {
            hasOptions: 'options' in configJsonTyped,
            optionsType: typeof configJsonTyped['options'],
            optionsIsArray: Array.isArray(configJsonTyped['options']),
            optionsValue: configJsonTyped['options'],
            hasValidation: 'validation' in configJsonTyped,
            validationType: typeof configJsonTyped['validation']
          });

          // Kiểm tra trường hợp options nằm trực tiếp trong configJson
          if (Array.isArray(configJsonTyped['options'])) {
            const rawOptions = configJsonTyped['options'] as Array<Record<string, unknown>>;
            selectOptions = rawOptions.map(opt => ({
              value: String(opt['value'] || ''),
              label: String(opt['label'] || opt['title'] || opt['value'] || ''),
            }));
            console.log('🔍 Options found in configJson.options:', selectOptions);
          }
          // Kiểm tra trường hợp options nằm trong validation.options
          else if (
            configJsonTyped['validation'] &&
            typeof configJsonTyped['validation'] === 'object'
          ) {
            const validation = configJsonTyped['validation'] as Record<string, unknown>;
            if (Array.isArray(validation['options'])) {
              const rawOptions = validation['options'] as Array<Record<string, unknown>>;
              selectOptions = rawOptions.map(opt => ({
                value: String(opt['value'] || ''),
                label: String(opt['label'] || opt['title'] || opt['value'] || ''),
              }));
              console.log('🔍 Options found in validation.options:', selectOptions);
            }
          }
        }

        // Kiểm tra nếu không có options thì hiển thị thông báo
        if (selectOptions.length === 0) {
          return (
            <div className="p-2 text-sm text-gray-500 bg-gray-100 dark:bg-gray-700 rounded">
              {t('business:product.form.customFields.noOptions', 'Không có tùy chọn nào được cấu hình')}
            </div>
          );
        }

        // Render select component với options
        return (
          <Select
            fullWidth
            value={value as string || ''}
            onChange={(val) => onChange(val as string)}
            options={selectOptions}
            placeholder={placeholder}
          />
        );
      }

      case 'boolean': {
        // Debug boolean field
        console.log('🔍 Boolean field debug:', {
          fieldLabel: field.label,
          rawValue: value,
          valueType: typeof value,
          valueString: String(value),
          isTrue: value === true,
          isFalse: value === false,
          isStringTrue: value === 'true',
          isStringFalse: value === 'false'
        });

        // Xử lý value cho boolean - hỗ trợ cả boolean và string
        let selectValue = '';
        if (value === true || value === 'true') {
          selectValue = 'true';
        } else if (value === false || value === 'false') {
          selectValue = 'false';
        }

        console.log('🔍 Boolean select value:', selectValue);

        return (
          <Select
            fullWidth
            value={selectValue}
            onChange={(val) => {
              console.log('🔍 Boolean onChange:', { val, valType: typeof val });
              if (val === 'true') {
                onChange(true);
              } else if (val === 'false') {
                onChange(false);
              } else {
                onChange('');
              }
            }}
            options={[
              { value: 'true', label: t('common:yes', 'Có') },
              { value: 'false', label: t('common:no', 'Không') },
            ]}
            placeholder={placeholder || t('business:product.form.customFields.selectBoolean', 'Chọn Đúng/Sai')}
          />
        );
      }

      case 'checkbox':
        return (
          <Checkbox
            checked={Boolean(value)}
            onChange={(checked) => onChange(checked)}
            label={placeholder}
          />
        );

      case 'switch':
        return (
          <Toggle
            checked={Boolean(value)}
            onChange={(checked: boolean) => onChange(checked)}
            label={placeholder}
          />
        );

      case 'radio':
        return (
          <div className="space-y-2">
            {options.map((option, index: number) => (
              <label key={index} className="flex items-center space-x-2">
                <input
                  type="radio"
                  name={`radio-${field.id}`}
                  value={String(option.value)}
                  checked={value === option.value}
                  onChange={(e) => onChange(e.target.value)}
                  className="form-radio"
                />
                <span>{option.label}</span>
              </label>
            ))}
          </div>
        );

      case 'text':
      default:
        return (
          <div>
            <Input
              fullWidth
              type="text"
              value={value as string || ''}
              onChange={(e) => handleChange(e.target.value)}
              placeholder={placeholder}
              minLength={minLength}
              maxLength={maxLength}
              pattern={pattern}
            />
            {error && (
              <Typography variant="caption" className="text-red-500 mt-1">
                {error}
              </Typography>
            )}
          </div>
        );
    }
  };

  return (
    <div className="mb-4">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <Typography variant="body2" className="font-medium text-foreground">
            {field.label}
          </Typography>
          {field.required && (
            <span className="text-red-500 text-xs">*</span>
          )}
          <span className="text-xs text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
            {field.component || field.type || 'text'}
          </span>
        </div>
        <button
          type="button"
          onClick={onRemove}
          className="text-gray-400 hover:text-red-500 text-sm transition-colors"
          title="Xóa trường này"
        >
          ✕
        </button>
      </div>
      {renderInput()}
    </div>
  );
};

export default CustomFieldRenderer;
