import React from 'react';
import { useTranslation } from 'react-i18next';
import { Controller } from 'react-hook-form';
import {
  CollapsibleCard,
  Typography,
  FormItem,
  Input,
  Textarea,
  Chip,
} from '@/shared/components/common';

interface GeneralInfoSectionProps {
  tempTags: string[];
  setTempTags: (tags: string[]) => void;
}

const GeneralInfoSection: React.FC<GeneralInfoSectionProps> = ({ tempTags, setTempTags }) => {
  const { t } = useTranslation(['business', 'common']);
  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="font-medium">
          {t('business:product.form.sections.generalInfo', '1. Thông tin chung')}
        </Typography>
      }
      defaultOpen={true}
      className="mb-4"
    >
      <div className="space-y-4">
        <FormItem name="name" label={t('business:product.name')} required>
          <Input fullWidth placeholder={t('business:product.form.namePlaceholder')} />
        </FormItem>

        <FormItem name="description" label={t('business:product.form.description')}>
          <Textarea
            fullWidth
            rows={4}
            placeholder={t('business:product.form.descriptionPlaceholder')}
          />
        </FormItem>

        <FormItem name="tags" label={t('business:product.tags')}>
          <Controller
            name="tags"
            render={({ field }) => (
              <div className="space-y-2">
                <Input
                  fullWidth
                  placeholder={t('business:product.form.tagsPlaceholder')}
                  onKeyDown={e => {
                    if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                      e.preventDefault();

                      // Lấy tag mới
                      const newTag = e.currentTarget.value.trim();

                      // Thêm tag mới nếu chưa tồn tại
                      if (!tempTags.includes(newTag)) {
                        const newTags = [...tempTags, newTag];
                        setTempTags(newTags);
                        field.onChange(newTags); // Đồng bộ với form
                      }

                      e.currentTarget.value = '';
                    }
                  }}
                />
                <div className="flex flex-wrap gap-1 mt-2">
                  {tempTags.map((tag, tagIndex) => (
                    <Chip
                      key={`tag-${tagIndex}-${tag}`}
                      size="sm"
                      closable
                      onClose={() => {
                        const newTags = tempTags.filter(t => t !== tag);
                        setTempTags(newTags);
                        field.onChange(newTags); // Đồng bộ với form
                      }}
                    >
                      {tag}
                    </Chip>
                  ))}
                </div>
              </div>
            )}
          />
        </FormItem>
      </div>
    </CollapsibleCard>
  );
};

export default GeneralInfoSection;
