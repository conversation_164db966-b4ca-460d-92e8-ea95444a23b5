import React from 'react';
import {
  CollapsibleCard,
  Typography,
  FormItem,
  Input,
  Textarea,
} from '@/shared/components/common';
import { GeneralInfoSectionProps } from './product-form-types';

const GeneralInfoSection: React.FC<GeneralInfoSectionProps> = ({ t }) => {
  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="font-medium">
          {t('business:product.form.sections.generalInfo', '1. Thông tin chung')}
        </Typography>
      }
      defaultOpen={true}
      className="mb-4"
    >
      <div className="space-y-4">
        <FormItem name="name" label={t('business:product.name')} required>
          <Input fullWidth placeholder={t('business:product.form.namePlaceholder')} />
        </FormItem>

        <FormItem name="description" label={t('business:product.form.description')}>
          <Textarea
            fullWidth
            rows={4}
            placeholder={t('business:product.form.descriptionPlaceholder')}
          />
        </FormItem>
      </div>
    </CollapsibleCard>
  );
};

export default GeneralInfoSection;
