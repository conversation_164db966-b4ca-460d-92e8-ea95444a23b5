/**
 * <PERSON><PERSON><PERSON> các trường truthy từ đối tượng params
 * @param params Đối tượng params cần lọc
 * @returns Đối tượng mới chỉ chứa các trường truthy
 */
export const filterTruthyParams = <T extends Record<string, any>>(params?: T): Partial<T> => {
  if (!params) return {};
  
  return Object.entries(params).reduce((filtered, [key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      filtered[key as keyof T] = value;
    }
    return filtered;
  }, {} as Partial<T>);
};