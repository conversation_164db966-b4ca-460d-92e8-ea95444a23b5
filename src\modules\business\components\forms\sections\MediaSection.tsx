import React from 'react';
import {
  CollapsibleCard,
  Typography,
  FormItem,
} from '@/shared/components/common';
import { MultiFileUpload } from '@/shared/components/common/MultiFileUpload';
import { TagInput } from '@/shared/components/common/TagInput';
import { MediaSectionProps } from './product-form-types';

const MediaSection: React.FC<MediaSectionProps> = ({ 
  t, 
  mediaFiles, 
  setMediaFiles, 
  tempTags, 
  setTempTags 
}) => {
  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="font-medium">
          {t('business:product.form.sections.media', '3. Hình ảnh sản phẩm')}
        </Typography>
      }
      defaultOpen={false}
      className="mb-4"
    >
      <div className="space-y-4">
        <FormItem name="tags" label={t('business:product.form.tags')}>
          <TagInput
            fullWidth
            value={tempTags}
            onChange={setTempTags}
            placeholder={t('business:product.form.tagsPlaceholder')}
          />
        </FormItem>

        <FormItem name="media" label={t('business:product.form.media')}>
          <MultiFileUpload
            accept="image/*"
            mediaOnly={true}
            placeholder={t('business:product.form.mediaPlaceholder')}
            onChange={setMediaFiles}
            value={mediaFiles}
          />
        </FormItem>
      </div>
    </CollapsibleCard>
  );
};

export default MediaSection;
